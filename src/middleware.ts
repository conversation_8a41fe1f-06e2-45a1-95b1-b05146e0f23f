import { NextRequest, NextResponse } from 'next/server';
import { existsSync } from 'fs';
import { join } from 'path';

// Define routes that should use index.tsx instead of page.tsx
const INDEX_ROUTES = [
    '/auth',
    '/dashboard',
    '/talent-pool',
    '/flow-history',
    '/flow-publish',
    '/job-manage',
    '/resume-detail',
    '/interviewer',
    '/interview-time',
    '/candidate',
    '/my-interview',
    '/job-detail',
    '/job-edit',
    '/elimination-pool',
    '/chat',
    '/new-chat',
    '/settings'
];

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    
    // Skip API routes and static files
    if (
        pathname.startsWith('/api/') ||
        pathname.startsWith('/_next/') ||
        pathname.startsWith('/static/') ||
        pathname.includes('.')
    ) {
        return NextResponse.next();
    }
    
    // Handle root path
    if (pathname === '/') {
        return NextResponse.next();
    }
    
    // Check if this is a route that should use index.tsx
    const isIndexRoute = INDEX_ROUTES.some(route => pathname.startsWith(route));
    
    if (isIndexRoute) {
        // Let Next.js handle the routing - our webpack plugin will resolve index.tsx
        return NextResponse.next();
    }
    
    return NextResponse.next();
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
    ],
};
