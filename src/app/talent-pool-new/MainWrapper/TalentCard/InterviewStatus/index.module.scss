.interview-status {
    .interview-status-right {
        max-width: 80%;
        overflow: hidden;
        .interview-status-content {
            margin: 0 4px;
            display: flex;
            align-items: center;
        }
    }
}

.interview-status-popover {
    :global {
        .ant-popover-inner {
            width: 360px;
            max-height: 300px;
            overflow: auto;
            padding: 16px;
        }
    }

    .interview-detail__header {
        margin-bottom: 8px;
        cursor: pointer;

        .name {
            color: var(--main-text-color);
            font-weight: 600;
            line-height: 22px;
        }
    }

    .job-collapse-wrapper__content {
        margin-bottom: 12px;
        overflow: hidden;
        transition: all 0.25s linear;

        &.expanded {
            max-height: 1000px; // 设置一个足够大的值
            opacity: 1;
        }

        &.collapsed {
            max-height: 0;
            opacity: 0;
            padding-top: 0;
            padding-bottom: 0;
        }

        .nick-name {
            line-height: 22px;
            color: var(--main-text-color);
        }
        .time {
            line-height: 19px;
            color: var(--sub-text-color);
        }
    }
}
