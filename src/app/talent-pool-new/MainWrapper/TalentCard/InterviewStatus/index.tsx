// import { JobInterviewStatusResp } from "@/app/views/Candidate/typing";
import { useEffect, useRef, useState } from "react";
import EllipsisIcon from "@/app/icons/EllipsisIcon.svg";
import { Avatar, Flex, Popover } from "antd";
import { RightOutlined, UpOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

export interface StatusList {
    id: string;
    statusName: string;
    reason: string;
}

const InterviewStatus = ({ allStatusList }: { allStatusList: StatusList[] }) => {
    const [isShowAllStatus, setIsShowAllStatus] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [currentStatus, setCurrentStatus] = useState<any>("");
    const [overflow, setOverFlow] = useState(false);

    const outerRef = useRef<HTMLDivElement>(null);
    const innerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const timer = setTimeout(() => {
            if (outerRef.current && innerRef.current) {
                const overflow = innerRef.current.scrollWidth >= outerRef.current.clientWidth;
                setOverFlow(overflow);
            }

            clearTimeout(timer);
        }, 300);
    }, []);

    const InterviewDetail = ({ item }: { item: any }) => {
        const [collapsed, setCollapsed] = useState(false);

        return (
            <div className={styles["interview-detail-wrapper"]}>
                <div
                    className={styles["interview-detail__header"]}
                    onClick={(e) => {
                        e.stopPropagation();
                        setCollapsed((prev) => !prev);
                    }}
                >
                    <Flex gap={8} align="center">
                        <UpOutlined
                            style={{
                                rotate: collapsed ? "90deg" : "0deg",
                                transition: "all 0.5s",
                                padding: 4,
                                backgroundColor: "#0000000f",
                                borderRadius: 4,
                            }}
                        />
                        <span className={styles["name"]}>{item?.name}</span>
                    </Flex>
                </div>
                <div
                    className={`${styles["job-collapse-wrapper__content"]} ${collapsed ? styles["collapsed"] : styles["expanded"]}`}
                >
                    <Flex justify="space-between" align="center">
                        <Flex gap={12}>
                            <Avatar size={40} src={item.avatar} />
                            <Flex vertical>
                                <span className={styles["nick-name"]}>{item.nickName}</span>
                                <span className={styles["time"]}>{item.time}</span>
                            </Flex>
                        </Flex>
                        <Flex
                            gap={4}
                            align="center"
                            style={{
                                backgroundColor: item.background ? `var(${item.background})` : "",
                                padding: "4px 8px",
                                borderRadius: 50,
                            }}
                        >
                            <span className={styles["status"]}>{item.statusName}</span>
                        </Flex>
                    </Flex>
                </div>
            </div>
        );
    };

    return (
        <div className={styles["interview-status"]}>
            <Popover
                classNames={{ root: `${styles["interview-status-popover"]}` }}
                content={() => {
                    return (
                        <div>
                            {allStatusList.map((item) => {
                                return <InterviewDetail key={item.id} item={item} />;
                            })}
                        </div>
                    );
                }}
                trigger="click"
            >
                {
                    <Flex
                        align="center"
                        onClick={(e) => {
                            e.stopPropagation();
                        }}
                    >
                        <div ref={outerRef} className={styles["interview-status-right"]}>
                            <Flex ref={innerRef} align="center" gap={4} className={styles["interview-status-content"]}>
                                {allStatusList.map((item, index) => {
                                    return (
                                        <Flex key={item.id} align="center" gap={4}>
                                            {item.statusName}
                                            {index !== allStatusList.length - 1 && (
                                                <RightOutlined style={{ color: "#0000006B", fontSize: 12 }} />
                                            )}
                                        </Flex>
                                    );
                                })}
                            </Flex>
                        </div>
                        {overflow && <EllipsisIcon />}
                    </Flex>
                }
            </Popover>
        </div>
    );
};

export default InterviewStatus;
