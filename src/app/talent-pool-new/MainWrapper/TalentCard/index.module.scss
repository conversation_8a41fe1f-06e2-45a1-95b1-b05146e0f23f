.talent-card {
    padding: 16px 12px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;

    .card-header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .header-left {
            display: flex;
            align-items: center;
            width: calc(100% - 200px);
            .header-name {
                margin-left: 8px;
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: 24px; /* 150% */
                min-width: 50px;
            }
            .header-base-info {
                display: flex;
                align-items: center;
                gap: 4px;

                .gender {
                    min-width: 30px;
                }
                .age {
                    min-width: 35px;
                }
                .work-experience {
                    min-width: 80px;
                }

                .header-base-info-divider {
                    display: block;
                    width: 4px;
                    height: 4px;
                    background-color: #949494;
                    border-radius: 50%;
                }
            }
        }

        .header-right {
            width: 200px;
            .header-right-time {
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                color: rgba(0, 0, 0, 0.6);
                display: block;
            }

            .header-right-operate {
                position: absolute;
                top: 0;
                right: 0;
                display: none;
                align-items: center;
                gap: 12px;
            }
        }
    }

    .card-content {
        padding: 6px 0;
        .card-content-info {
            .info-column {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .evaluate-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .evaluate-grade {
                        // font-size: 16px;
                    }
                }

                .rate-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    .rate {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .card-footer {
        border-radius: 4px;
        background: linear-gradient(90deg, #f5f0ff 0%, rgba(255, 255, 255, 0.1) 100%);
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
        color: #000000db;
        .ai-suggestion-prefix {
            width: 100px;
        }
        .ai-suggestion-content {
            width: calc(100% - 100px);
        }
    }

    &:hover {
        background-color: #f5f5f5;
        .header-right {
            .header-right-time {
                display: none;
            }
            .header-right-operate {
                display: flex;
            }
        }
    }
}
