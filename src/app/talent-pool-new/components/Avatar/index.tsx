import { Avatar as AntAvatar } from "antd";

interface AvatarProps {
    color: string;
    content: string;
    style?: React.CSSProperties;
    size?: number | "large" | "small" | "default" | { xs: number; sm: number; md: number; lg: number; xl: number };
}

/** Avatar组件 - 显示用户头像
 * @param color - 头像背景颜色
 * @param content - 显示内容，将取首字符显示
 * @param contentStyle - 内容样式
 * @returns JSX.Element
 */
const Avatar: React.FC<AvatarProps> = (props) => {
    const { color, content, style } = props;
    // 取content的首字符作为显示内容
    const displayContent = content ? content.charAt(0) : "";
    // 合并背景色、对齐方式和内容样式
    const mergedStyle = {
        backgroundColor: color,
        verticalAlign: "center",
        ...(style || {}), // 用户传入的其他样式
    };

    return (
        <AntAvatar {...props} style={mergedStyle}>
            {displayContent}
        </AntAvatar>
    );
};

export default Avatar;
