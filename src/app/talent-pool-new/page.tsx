import React, { useState, useCallback, useEffect } from "react";
import { Layout } from "antd";
import LeftWrapper from "./LeftWrapper";
import MainWrapper from "./MainWrapper";
import { useFilterState, type FilterState } from "../../hooks/useFilterState";
import styles from "./index.module.scss";
import { useNavigate } from "react-router-dom";
import { Path } from "@/app/constant";
import { CandidateListResp } from "@/app/store/modules/candidate";

/** TalentPoolNew组件属性接口 */
interface TalentPoolNewProps {
    /** 初始搜索关键词 */
    initialKeyword?: string;
    /** 初始筛选条件 */
    initialFilters?: Record<string, any>;
}

/**
 * 人才库新版组件
 * @param props TalentPoolNew组件属性
 * @returns TalentPoolNew组件
 */
const TalentPoolNew: React.FC<TalentPoolNewProps> = ({ initialKeyword = "", initialFilters = {} }) => {
    const navigate = useNavigate();

    // 使用增强的筛选状态Hook
    const {
        filters,
        pagination,
        loadingState,
        updateFilter,
        updateFilters,
        resetFilters,
        updatePagination,
        goToPage,
        changePageSize,
        setLoading,
        setSearching,
        activeFilterCount,
        hasFilters,
    } = useFilterState({
        initialFilters: {
            ...initialFilters,
            keyword: initialKeyword,
        },
        defaultPageSize: 20,
    });

    // 选中的菜单项
    const [selectedMenuKeys, setSelectedMenuKeys] = useState<string[]>(["all"]);

    // 人才列表数据
    const [talentList, setTalentList] = useState<CandidateListResp[]>([
        {
            profile: {
                applicantId: "3a2de49a-7971-4711-92ab-48f3adacfc09",
                name: "倪亦鸣",
                gender: "Unknown",
                birth: "1900.01.01",
                workExperienceYears: 0,
                graduationDate: "1900-01-01 00:00:00",
                phone: "1111111",
                email: "1111111",
                tags: {
                    hardRequirements: [1, 6, 10, 25, 67, 162],
                    capabilityFit: [192, 212, 332, 338, 339, 362, 370, 374, 380, 383],
                    potentialForecast: [494, 495, 499],
                },
                educationExperience: [
                    {
                        startDate: "1900-01-01 00:00:00",
                        endDate: "1900-01-01 00:00:00",
                        school: "南京信息工程大学",
                        degree: "本科",
                    },
                ],
                educationLevelId: "2",
                workExperience: [
                    {
                        startDate: "2022-07-01 00:00:00",
                        endDate: "2022-08-01 00:00:00",
                        company: "南京大学",
                        position: "Unknown Position",
                    },
                ],
                applications: [],
                applicantStatus: "候选中",
                competitions: [],
                createdAt: "2025-06-26 11:38:54",
                updatedAt: "2025-06-26 11:38:54",
                collectionId: "1",
                archived: false,
            },
            application: {
                applicantId: "3a2de49a-7971-4711-92ab-48f3adacfc09",
                jobId: "161",
                applicationDate: "2025-06-26 11:38:54",
                portraitAnalysis: {
                    matchScore: 0.0,
                    ranking: "0",
                    talentEstimate: "null",
                    projectExperienceCount: 0.0,
                    abilityLayerScore: {
                        hardRequirements: [],
                        hardRequirementsScore: 0.0,
                        capabilityFit: [],
                        capabilityFitScore: 0.0,
                        potentialForecast: [],
                        potentialForecastScore: 0.0,
                    },
                },
                portraitMatching: {
                    aiEvaluation:
                        "倪亦鸣同学具备扎实的Java后端开发能力，技术栈与岗位要求高度匹配，尤其在Redis分布式锁、线程池优化等系统性能相关技术上有实践应用。参与MyBatis开源项目并成功修复关键bug，展现了良好的代码能力与问题解决意识。但实习经历仅2个月且聚焦区块链课程建设，与人力资源系统业务关联度较低；项目经验集中在工业物联网领域，缺乏企业级管理系统开发经历。建议补充HR系统相关项目实践，加强微服务架构与DevOps工具链的深入理解，并在简历中明确HTTP/TCP协议的应用场景。",
                    simplifiedReview: "testaaaaa",
                },
                trackStageId: "8a09a3ec-d755-401e-ae9d-d6497e6f8633",
                trackStatusId: "389da082-2368-4b2f-8d31-59ae06811249",
                trackCurrentHandler: "10065971",
                trackCurrentStatus: 1,
                remarks: [],
                smsSendDate: "",
                smsSendResult: {
                    candidateId: "",
                    errMsg: "",
                    content: "",
                    gmtSend: "",
                    gmtReceive: "",
                    sendStatus: "",
                },
            },
            trackLogs: [],
        },
        {
            profile: {
                applicantId: "2f52afb9-ea9e-45d6-8a06-90d1412105d9",
                name: "刘佳琦",
                gender: "男",
                birth: "1900.01.01",
                workExperienceYears: 0,
                phone: "11111111",
                email: "11111111",
                graduationDate: "2025-06-01 00:00:00",
                tags: {
                    hardRequirements: [1, 5, 25, 156],
                    capabilityFit: [191, 194, 247, 332, 338, 339, 340, 362, 370, 374, 380, 383],
                    potentialForecast: [],
                },
                educationExperience: [
                    {
                        startDate: "2021-09-01 00:00:00",
                        endDate: "2025-06-01 00:00:00",
                        school: "中国石油大学",
                        degree: "本科",
                    },
                ],
                educationLevelId: "2",
                workExperience: [
                    {
                        startDate: "2024-08-01 00:00:00",
                        endDate: "2024-11-01 00:00:00",
                        company: "众安科技",
                        position: "后端开发",
                    },
                ],
                applications: [],
                applicantStatus: "候选中",
                competitions: [],
                createdAt: "2025-06-26 11:37:01",
                updatedAt: "2025-06-26 11:37:01",
                collectionId: "1",
                archived: false,
            },
            application: {
                applicantId: "2f52afb9-ea9e-45d6-8a06-90d1412105d9",
                jobId: "161",
                applicationDate: "2025-06-26 11:37:17",
                portraitAnalysis: {
                    matchScore: 0.0,
                    ranking: "0",
                    talentEstimate: "null",
                    projectExperienceCount: 0.0,
                    abilityLayerScore: {
                        hardRequirements: [],
                        hardRequirementsScore: 0.0,
                        capabilityFit: [],
                        capabilityFitScore: 0.0,
                        potentialForecast: [],
                        potentialForecastScore: 0.0,
                    },
                },
                portraitMatching: {
                    aiEvaluation:
                        "候选人具备扎实的Java后端开发能力，熟悉SpringBoot/SpringCloud框架及MySQL、Redis等数据库技术，与岗位要求的后端系统开发方向匹配。实习经历中参与保险系统接口开发及数据库设计，展示了模块开发与问题解决能力，符合人资系统开发需求。项目经验突出缓存优化、高并发处理等技术亮点，体现系统性能优化潜力。但需注意：1. 学历时间显示为2025年毕业，与JD要求的2026届应届生存在时间偏差；2. 项目多为个人开发，缺乏团队协作及需求评审相关经验描述；3. 未提及技术博客或开源项目。建议：明确毕业时间是否符合应届生要求，补充团队协作案例，并考虑增加技术分享成果以提升竞争力。",
                    simplifiedReview: "testaaaa",
                },
                trackStageId: "8a09a3ec-d755-401e-ae9d-d6497e6f8633",
                trackStatusId: "389da082-2368-4b2f-8d31-59ae06811249",
                trackCurrentHandler: "10065971",
                trackCurrentStatus: 1,
                remarks: [],
                smsSendDate: "",
                smsSendResult: {
                    candidateId: "",
                    errMsg: "",
                    content: "",
                    gmtSend: "",
                    gmtReceive: "",
                    sendStatus: "",
                },
            },
            trackLogs: [],
        },
    ]);
    /**
     * 处理主内容区搜索
     * @param keyword 搜索关键词
     */
    const handleSearch = useCallback(
        (keyword: string) => {
            updateFilter("keyword", keyword);
        },
        [updateFilter]
    );

    /** 处理筛选变化 */
    const handleMainFilterChange = useCallback(
        (newFilters: Partial<FilterState>) => {
            updateFilters(newFilters);
            // 这里可以触发数据加载
        },
        [updateFilters]
    );

    /** 处理分页变化 */
    const handlePageChange = useCallback(
        (page: number, pageSize: number) => {
            if (pageSize !== pagination.pageSize) {
                changePageSize(pageSize);
            } else {
                goToPage(page);
            }
            // 这里可以触发数据加载
        },
        [pagination.pageSize, changePageSize, goToPage]
    );

    /** 处理人才卡片点击 */
    const handleTalentClick = useCallback((talent: any) => {
        console.log("点击人才卡片:", talent);
        navigate(Path.ResumeDetail, { state: { talent } });
    }, []);

    /**
     * 处理左侧菜单变化
     * @param selectedKeys 选中的菜单项
     */
    const handleMenuChange = useCallback(
        (selectedKeys: string[]) => {
            setSelectedMenuKeys(selectedKeys);
            // 根据菜单选择更新筛选条件
            // updateFilter('category', selectedKeys);
        },
        [updateFilter]
    );

    /**
     * 处理左侧筛选条件变化
     * @param newFilters 新的筛选条件
     */
    const handleLeftFilterChange = useCallback(
        (newFilters: Partial<FilterState>) => {
            updateFilters(newFilters);
        },
        [updateFilters]
    );

    /**
     * 模拟API调用 - 根据筛选条件获取人才数据
     */
    const fetchTalentData = useCallback(async (filterParams: FilterState) => {
        // 这里应该调用实际的API
        // const response = await api.getTalentList(filterParams);
        // setTalentList(response.data);
    }, []);

    // 监听筛选条件变化，触发数据获取
    useEffect(() => {
        fetchTalentData(filters);
    }, [filters, fetchTalentData]);

    return (
        <div className={styles.talentPool}>
            <Layout className={styles.layout}>
                <Layout.Sider width={300} className={styles.sider}>
                    <LeftWrapper
                        onMenuChange={handleMenuChange}
                        onFilterChange={handleLeftFilterChange}
                        title="人才库"
                    />
                </Layout.Sider>
                <Layout.Content className={styles.content}>
                    <MainWrapper
                        talentList={talentList}
                        loading={loadingState.loading}
                        filters={filters}
                        pagination={pagination}
                        onSearch={handleSearch}
                        onFilterChange={handleMainFilterChange}
                        onResetFilters={resetFilters}
                        onPageChange={handlePageChange}
                        onTalentClick={handleTalentClick}
                        hasFilters={hasFilters}
                    />
                </Layout.Content>
            </Layout>
        </div>
    );
};

export default TalentPoolNew;
export type { TalentPoolNewProps };
