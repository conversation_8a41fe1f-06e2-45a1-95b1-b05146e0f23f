import React, { useState } from "react";
import { But<PERSON>, Divider } from "antd";
import TreeMenu from "./TreeMenu";
import { FolderOutlined, PlusOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import Avatar from "../components/Avatar";

/** 左侧包装器组件属性接口 */
interface LeftWrapperProps {
    /** 左侧包装器标题 */
    title?: string;
    /** 左侧包装器图标 */
    icon?: React.ReactNode;
    /** 选中的菜单项变化回调 */
    onMenuChange?: (selectedKeys: string[]) => void;
    /** 筛选条件变化回调 */
    onFilterChange?: (filters: Record<string, any>) => void;
}

/**
 * 左侧包装器组件
 * @param props LeftWrapper组件属性
 * @returns LeftWrapper组件
 */
const LeftWrapper: React.FC<LeftWrapperProps> = ({ title, icon, onMenuChange, onFilterChange }) => {
    // 选中的菜单项
    const [selectedMenuKeys, setSelectedMenuKeys] = useState<string[]>(["all"]);

    // 展开的菜单项
    const [expandedMenuKeys, setExpandedMenuKeys] = useState<string[]>(["all"]);

    // 当前筛选条件
    // const [currentFilters, setCurrentFilters] = useState<Record<string, any>>({});

    // 树形菜单数据
    const [treeData] = useState([
        {
            key: "all",
            title: "全部",
            icon: <FolderOutlined />,
            children: [
                { key: "public-talent", title: "公共人才库" },
                { key: "campus-talent", title: "校招人才" },
                { key: "social-talent", title: "社招人才" },
                { key: "new-graduate-2025", title: "2025 新进人才" },
            ],
        },
        { key: "talent-recommend", title: "人才推荐", icon: <FolderOutlined /> },
        { key: "my-attention", title: "我的关注", icon: <FolderOutlined /> },
        { key: "shared-talent", title: "共享人才", icon: <FolderOutlined /> },
    ]);

    const [folderList] = useState([
        {
            key: "2025-campus-recruitment",
            title: "2025校园招聘",
            icon: <Avatar size={20} style={{ fontSize: 12 }} color="#2f99f2" content="2025校园招聘" />,
            children: [
                { key: "factory", title: "数字工厂" },
                { key: "energy", title: "数字能源" },
                { key: "equipment", title: "数字装备" },
                { key: "hardware", title: "智能硬件" },
            ],
        },
        {
            key: "product-manager",
            title: "产品经理",
            icon: <Avatar size={20} style={{ fontSize: 12 }} color="#39d1cc" content="产品经理" />,
        },
        {
            key: "ued-designer",
            title: "UED设计",
            icon: <Avatar size={20} style={{ fontSize: 12 }} color="#87d068" content="UED设计" />,
        },
    ]);

    /**
     * 处理菜单选择变化
     * @param selectedKeys 选中的菜单项
     * @param info 选择信息
     */
    const handleMenuSelect = (selectedKeys: string[], info: any) => {
        setSelectedMenuKeys(selectedKeys);
        onMenuChange?.(selectedKeys);
    };

    /**
     * 处理菜单展开变化
     * @param expandedKeys 展开的菜单项
     * @param info 展开信息
     */
    const handleMenuExpand = (expandedKeys: string[], info: any) => {
        setExpandedMenuKeys(expandedKeys);
    };

    // /**
    //  * 处理筛选条件变化
    //  * @param filters 筛选条件
    //  */
    // const handleFilterChange = (filters: Record<string, any>) => {
    //     setCurrentFilters(filters);
    //     onFilterChange?.(filters);
    // };

    return (
        <div className={styles.leftWrapper}>
            <div className={styles["left-wrapper__header"]}>
                {icon}
                <span className={styles["left-wrapper__header__title"]}>{title}</span>
            </div>
            <div className={styles.content}>
                {/* 树形菜单 */}
                <TreeMenu
                    treeData={treeData}
                    // selectedKeys={selectedMenuKeys}
                    expandedKeys={expandedMenuKeys}
                    onSelect={handleMenuSelect}
                    onExpand={handleMenuExpand}
                />

                <Divider className={styles.divider} />
                <div className={styles["folder-header"]}>
                    <h3 style={{ margin: 0 }}>我的文件夹</h3>
                    <Button type="text" size="small" style={{ color: "#949494" }} icon={<PlusOutlined />}></Button>
                </div>
                {/* 筛选面板 */}
                <TreeMenu
                    treeData={folderList}
                    // selectedKeys={selectedMenuKeys}
                    expandedKeys={expandedMenuKeys}
                    onSelect={handleMenuSelect}
                    onExpand={handleMenuExpand}
                />
            </div>
        </div>
    );
};

export default LeftWrapper;
export type { LeftWrapperProps };
