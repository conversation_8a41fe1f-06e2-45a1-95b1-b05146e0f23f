.filter-panel {
  width: 100%;
  margin-top: 16px;

  .filter-collapse {
    .ant-collapse-item {
      border: none;
      
      .ant-collapse-header {
        padding: 8px 0;
        border-radius: 0;
        
        .ant-collapse-expand-icon {
          padding-inline-end: 8px;
        }
      }
      
      .ant-collapse-content {
        border: none;
        
        .ant-collapse-content-box {
          padding: 8px 0 16px 0;
        }
      }
    }
  }
}

.filter-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .filter-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
}

.filter-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .filter-option {
    display: flex;
    align-items: center;
    
    .ant-checkbox-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      
      .filter-option-label {
        flex: 1;
        font-size: 13px;
        color: #666;
      }
      
      .filter-option-count {
        font-size: 12px;
        color: #999;
        margin-left: 4px;
      }
    }
    
    &:hover {
      .filter-option-label {
        color: #1890ff;
      }
    }
  }
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .filter-tag {
    cursor: pointer;
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
    margin: 0;
    transition: all 0.2s;
    
    .filter-tag-count {
      margin-left: 4px;
      opacity: 0.7;
    }
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filter-panel {
    margin-top: 12px;
    
    .filter-group-title {
      font-size: 13px;
    }
    
    .filter-option-label {
      font-size: 12px;
    }
    
    .filter-tag {
      font-size: 11px;
      padding: 1px 6px;
    }
  }
}