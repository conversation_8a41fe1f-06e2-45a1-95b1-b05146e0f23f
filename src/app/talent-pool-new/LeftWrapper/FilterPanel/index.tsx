import React, { useState } from "react";
import { Collapse, Checkbox, Tag } from "antd";
import { DownOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

/** 筛选项数据接口 */
interface FilterOption {
    key: string;
    label: string;
    count?: number;
}

/** 筛选分组数据接口 */
interface FilterGroup {
    key: string;
    title: string;
    options: FilterOption[];
    multiple?: boolean; // 是否支持多选
}

/** FilterPanel组件属性接口 */
interface FilterPanelProps {
    /** 筛选分组数据 */
    filterGroups: FilterGroup[];
    /** 选中的筛选项 */
    selectedFilters?: Record<string, string[]>;
    /** 筛选项变化回调 */
    onFilterChange?: (groupKey: string, selectedValues: string[]) => void;
}

/**
 * 筛选面板组件
 * @param props FilterPanel组件属性
 * @returns FilterPanel组件
 */
const FilterPanel: React.FC<FilterPanelProps> = ({ filterGroups, selectedFilters = {}, onFilterChange }) => {
    const [activeKeys, setActiveKeys] = useState<string[]>(filterGroups.map((group) => group.key));

    /**
     * 处理筛选项变化
     * @param groupKey 分组key
     * @param checkedValues 选中的值
     */
    const handleFilterChange = (groupKey: string, checkedValues: string[]) => {
        onFilterChange?.(groupKey, checkedValues);
    };

    /**
     * 渲染筛选选项
     * @param group 筛选分组
     * @returns 筛选选项JSX
     */
    const renderFilterOptions = (group: FilterGroup) => {
        const selectedValues = selectedFilters[group.key] || [];

        if (group.multiple) {
            return (
                <Checkbox.Group
                    value={selectedValues}
                    onChange={(values) => handleFilterChange(group.key, values as string[])}
                    className={styles["filter-checkbox-group"]}
                >
                    {group.options.map((option) => (
                        <div key={option.key} className={styles["filter-option"]}>
                            <Checkbox value={option.key}>
                                <span className={styles["filter-option-label"]}>{option.label}</span>
                                {option.count && (
                                    <span className={styles["filter-option-count"]}>({option.count})</span>
                                )}
                            </Checkbox>
                        </div>
                    ))}
                </Checkbox.Group>
            );
        }

        return (
            <div className={styles["filter-tags"]}>
                {group.options.map((option) => {
                    const isSelected = selectedValues.includes(option.key);
                    return (
                        <Tag
                            key={option.key}
                            color={isSelected ? "blue" : "default"}
                            className={styles["filter-tag"]}
                            onClick={() => {
                                const newValues = isSelected
                                    ? selectedValues.filter((v) => v !== option.key)
                                    : [option.key];
                                handleFilterChange(group.key, newValues);
                            }}
                        >
                            {option.label}
                            {option.count && <span className={styles["filter-tag-count"]}>({option.count})</span>}
                        </Tag>
                    );
                })}
            </div>
        );
    };

    const collapseItems = filterGroups.map((group) => ({
        key: group.key,
        label: (
            <div className={styles["filter-group-header"]}>
                <span className={styles["filter-group-title"]}>{group.title}</span>
            </div>
        ),
        children: renderFilterOptions(group),
    }));

    return (
        <div className={styles["filter-panel"]}>
            <Collapse
                activeKey={activeKeys}
                onChange={setActiveKeys}
                expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
                items={collapseItems}
                className={styles["filter-collapse"]}
                ghost
            />
        </div>
    );
};

export default FilterPanel;
export type { FilterOption, FilterGroup, FilterPanelProps };
