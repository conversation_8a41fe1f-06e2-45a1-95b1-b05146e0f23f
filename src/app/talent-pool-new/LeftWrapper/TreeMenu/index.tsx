import React, { Key } from "react";
import { Tree } from "antd";
import { DownOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

/** 树形节点数据接口 */
interface TreeNode {
    key: string;
    title: string;
    children?: TreeNode[];
    count?: number;
}

/** TreeMenu组件属性接口 */
interface TreeMenuProps {
    /** 树形数据 */
    treeData: TreeNode[];
    /** 选中的节点key */
    selectedKeys?: string[];
    /** 展开的节点key */
    expandedKeys?: string[];
    /** 节点选中回调 */
    onSelect?: (selectedKeys: any[], info: any) => void;
    /** 节点展开回调 */
    onExpand?: (expandedKeys: any[], info: any) => void;
}

/**
 * 树形菜单组件
 * @param props TreeMenu组件属性
 * @returns TreeMenu组件
 */
const TreeMenu: React.FC<TreeMenuProps> = ({ treeData, selectedKeys = [], expandedKeys = [], onSelect, onExpand }) => {
    /**
     * 自定义树节点渲染
     * @param nodeData 节点数据
     * @returns 渲染的节点内容
     */
    const titleRender = (nodeData: TreeNode) => (
        <div className={styles["tree-node-content"]}>
            <span className={styles["tree-node-title"]}>{nodeData.title}</span>
            {nodeData.count && <span className={styles["tree-node-count"]}>({nodeData.count})</span>}
        </div>
    );

    return (
        <div className={styles["tree-menu"]}>
            <Tree
                blockNode
                showIcon
                switcherIcon={<DownOutlined />}
                treeData={treeData}
                selectedKeys={selectedKeys}
                expandedKeys={expandedKeys}
                onSelect={onSelect}
                onExpand={onExpand}
                // titleRender={titleRender}
            />
        </div>
    );
};

export default TreeMenu;
export type { TreeNode, TreeMenuProps };
