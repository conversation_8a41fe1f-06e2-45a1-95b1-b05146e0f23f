/* eslint-disable react-hooks/rules-of-hooks */
import { useDebouncedCallback } from "use-debounce";
import React, { Fragment, RefObject, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { markdownToTxt } from "markdown-to-txt";
import LoadingIcon from "../../icons/three-dots.svg";
// import LoadingButtonIcon from "../../icons/loading.svg";
import DeleteIcon from "../../icons/clear.svg";
import ConfirmIcon from "../../icons/confirm.svg";
// import CloseIcon from "../../icons/close.svg";
import CancelIcon from "../../icons/cancel.svg";
import LightIcon from "../../icons/light.svg";
import DarkIcon from "../../icons/dark.svg";
import AutoIcon from "../../icons/auto.svg";
import StopIcon from "../../icons/pause.svg";
import SizeIcon from "../../icons/size.svg";
import QualityIcon from "../../icons/hd.svg";
import StyleIcon from "../../icons/palette.svg";
import EditIcon from "../../icons/chat/edit.svg";
import CopyIcon from "../../icons/chat/copy.svg";
import RetryIcon from "../../icons/chat/retry.svg";
import StarIcon from "../../icons/chat/star.svg";
import ShareIcon from "../../icons/chat/share.svg";
import LikeIcon from "../../icons/chat/like.svg";
import DislikeIcon from "../../icons/chat/dislike.svg";

import {
    ChatMessage,
    createMessage,
    DEFAULT_TOPIC,
    ModelType,
    SubmitKey,
    Theme,
    useAccessStore,
    useAppConfig,
    useChatStore,
} from "../../store";

import {
    autoGrowTextArea,
    copyToClipboard,
    getMessageImages,
    getMessageTextContent,
    isDalle3,
    isVisionModel,
    safeLocalStorage,
    getModelSizes,
    supportsCustomSize,
    useMobileScreen,
    // selectOrCopy,
} from "../../utils/utils";

import { uploadImage as uploadImageRemote } from "../../utils/chat";
import dynamic from "next/dynamic";
import { ChatControllerPool } from "../../client/controller";
import { DalleQuality, DalleStyle, ModelSize } from "../../types/typing";
import { Prompt, usePromptStore } from "../../store/prompt";
import Locale from "../../locales";
import { IconButton } from "../../components/Button";
import styles from "./index.module.scss";
// showPrompt
import { List, ListItem, Modal, Selector, showConfirm, showToast } from "../ui-lib";
import { useNavigate } from "react-router-dom";
import {
    CHAT_PAGE_SIZE,
    DEFAULT_TTS_ENGINE,
    ModelProvider,
    Path,
    REQUEST_TIMEOUT_MS,
    ServiceProvider,
    UNFINISHED_INPUT,
} from "../../constant";
import { ChatCommandPrefix, useChatCommand, useCommand } from "../../command";
import { prettyObject } from "../../utils/format";
import { ExportMessageModal } from "../exporter";
// import { getClientConfig } from "../config/client";
import { useAllModels } from "../../utils/hooks";
// MultimodalContent
import { ClientApi } from "../../client/api";
import { createTTSPlayer } from "../../utils/audio";
import { MsEdgeTTS, OUTPUT_FORMAT } from "../../utils/ms_edge_tts";
import { isEmpty } from "lodash-es";
import { getModelProvider } from "../../utils/model";
// import { RealtimeChat } from "@/app/components/realtime-chat";
import clsx from "clsx";
import { AttachedFile, ChatInput } from "../../components/ChatInput";
import { Avatar, Button, Divider, FloatButton, message } from "antd";
import ChatIcon from "../../icons/chat/chat-logo.svg";
import ToBottomIcon from "../../icons/chat/toBottom.svg";
import { useTitleStore } from "../../store/modules/chat-content/title";

// react-markdown解析
// const Markdown = dynamic(async () => (await import("./markdown")).Markdown, {
//     loading: () => <LoadingIcon />,
// });
// mdx解析
const MDXMarkdown = dynamic(async () => (await import("@/app/Markdown/MDX-markdown")).MDXMarkdown, {
    loading: () => <LoadingIcon />,
});

const localStorage = safeLocalStorage();
const ttsPlayer = createTTSPlayer();

function useSubmitHandler() {
    const config = useAppConfig();
    const submitKey = config.submitKey;
    const isComposing = useRef(false);

    useEffect(() => {
        const onCompositionStart = () => {
            isComposing.current = true;
        };
        const onCompositionEnd = () => {
            isComposing.current = false;
        };

        window.addEventListener("compositionstart", onCompositionStart);
        window.addEventListener("compositionend", onCompositionEnd);

        return () => {
            window.removeEventListener("compositionstart", onCompositionStart);
            window.removeEventListener("compositionend", onCompositionEnd);
        };
    }, []);

    const shouldSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key !== "Enter") return false;
        if (e.key === "Enter" && (e.nativeEvent.isComposing || isComposing.current)) return false;
        return (
            (config.submitKey === SubmitKey.AltEnter && e.altKey) ||
            (config.submitKey === SubmitKey.CtrlEnter && e.ctrlKey) ||
            (config.submitKey === SubmitKey.ShiftEnter && e.shiftKey) ||
            (config.submitKey === SubmitKey.MetaEnter && e.metaKey) ||
            (config.submitKey === SubmitKey.Enter && !e.altKey && !e.ctrlKey && !e.shiftKey && !e.metaKey)
        );
    };

    return {
        submitKey,
        shouldSubmit,
    };
}

export type RenderPrompt = Pick<Prompt, "title" | "content">;

export function PromptHints(props: { prompts: RenderPrompt[]; onPromptSelect: (prompt: RenderPrompt) => void }) {
    const noPrompts = props.prompts.length === 0;
    const [selectIndex, setSelectIndex] = useState(0);
    const selectedRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        setSelectIndex(0);
    }, [props.prompts.length]);

    useEffect(() => {
        const onKeyDown = (e: KeyboardEvent) => {
            if (noPrompts || e.metaKey || e.altKey || e.ctrlKey) {
                return;
            }
            // arrow up / down to select prompt
            const changeIndex = (delta: number) => {
                e.stopPropagation();
                e.preventDefault();
                const nextIndex = Math.max(0, Math.min(props.prompts.length - 1, selectIndex + delta));
                setSelectIndex(nextIndex);
                selectedRef.current?.scrollIntoView({
                    block: "center",
                });
            };

            if (e.key === "ArrowUp") {
                changeIndex(1);
            } else if (e.key === "ArrowDown") {
                changeIndex(-1);
            } else if (e.key === "Enter") {
                const selectedPrompt = props.prompts.at(selectIndex);
                if (selectedPrompt) {
                    props.onPromptSelect(selectedPrompt);
                }
            }
        };

        window.addEventListener("keydown", onKeyDown);

        return () => window.removeEventListener("keydown", onKeyDown);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.prompts.length, selectIndex]);

    if (noPrompts) return null;
    return (
        <div className={styles["prompt-hints"]}>
            {props.prompts.map((prompt, i) => (
                <div
                    ref={i === selectIndex ? selectedRef : null}
                    className={clsx(styles["prompt-hint"], {
                        [styles["prompt-hint-selected"]]: i === selectIndex,
                    })}
                    key={prompt.title + i.toString()}
                    onClick={() => props.onPromptSelect(prompt)}
                    onMouseEnter={() => setSelectIndex(i)}
                >
                    <div className={styles["hint-title"]}>{prompt.title}</div>
                    <div className={styles["hint-content"]}>{prompt.content}</div>
                </div>
            ))}
        </div>
    );
}

function ClearContextDivider() {
    const chatStore = useChatStore();
    const session = chatStore.currentSession();

    return (
        <div
            className={styles["clear-context"]}
            onClick={() => chatStore.updateTargetSession(session, (session) => (session.clearContextIndex = undefined))}
        >
            <div className={styles["clear-context-tips"]}>{Locale.Context.Clear}</div>
            <div className={styles["clear-context-revert-btn"]}>{Locale.Context.Revert}</div>
        </div>
    );
}

export function ChatAction(props: { text: string; icon: JSX.Element; onClick: () => void }) {
    const iconRef = useRef<HTMLDivElement>(null);
    const textRef = useRef<HTMLDivElement>(null);
    const [width, setWidth] = useState({
        full: 16,
        icon: 16,
    });

    function updateWidth() {
        if (!iconRef.current || !textRef.current) return;
        const getWidth = (dom: HTMLDivElement) => dom.getBoundingClientRect().width;
        const textWidth = getWidth(textRef.current);
        const iconWidth = getWidth(iconRef.current);
        setWidth({
            full: textWidth + iconWidth,
            icon: iconWidth,
        });
    }

    return (
        <div
            className={clsx(styles["chat-input-action"], "clickable")}
            onClick={() => {
                props.onClick();
                setTimeout(updateWidth, 1);
            }}
            onMouseEnter={updateWidth}
            onTouchStart={updateWidth}
            style={
                {
                    "--icon-width": `${width.icon}px`,
                    "--full-width": `${width.full}px`,
                } as React.CSSProperties
            }
        >
            <div ref={iconRef} className={styles["icon"]}>
                {props.icon}
            </div>
            <div className={styles["text"]} ref={textRef}>
                {props.text}
            </div>
        </div>
    );
}

function useScrollToBottom(scrollRef: RefObject<HTMLDivElement>, detach: boolean = false, messages: ChatMessage[]) {
    // for auto-scroll
    const [autoScroll, setAutoScroll] = useState(true);
    const scrollDomToBottom = useCallback(() => {
        const dom = scrollRef.current;
        if (dom) {
            requestAnimationFrame(() => {
                setAutoScroll(true);
                dom.scrollTo(0, dom.scrollHeight);
            });
        }
    }, [scrollRef]);

    // auto scroll
    useEffect(() => {
        if (autoScroll && !detach) {
            scrollDomToBottom();
        }
    });

    // auto scroll when messages length changes
    const lastMessagesLength = useRef(messages.length);
    useEffect(() => {
        if (messages.length > lastMessagesLength.current && !detach) {
            scrollDomToBottom();
        }
        lastMessagesLength.current = messages.length;
    }, [messages.length, detach, scrollDomToBottom]);

    return {
        scrollRef,
        autoScroll,
        setAutoScroll,
        scrollDomToBottom,
    };
}

// ChatActions组件是输入框上面一栏的功能模块
export function ChatActions(props: {
    uploadImage: () => void;
    setAttachImages: (images: string[]) => void;
    setUploading: (uploading: boolean) => void;
    showPromptHints: () => void;
    uploading: boolean;
    setShowShortcutKeyModal: React.Dispatch<React.SetStateAction<boolean>>;
    setUserInput: (input: string) => void;
}) {
    const config = useAppConfig();
    const navigate = useNavigate();
    const chatStore = useChatStore();
    const session = chatStore.currentSession();

    // switch themes
    const theme = config.theme;

    function nextTheme() {
        const themes = [Theme.Auto, Theme.Light, Theme.Dark];
        const themeIndex = themes.indexOf(theme);
        const nextIndex = (themeIndex + 1) % themes.length;
        const nextTheme = themes[nextIndex];
        config.update((config) => (config.theme = nextTheme));
    }

    // stop all responses
    const couldStop = ChatControllerPool.hasPending();
    const stopAll = () => ChatControllerPool.stopAll();

    // switch model
    const currentModel = session.modelConfig.model;
    const currentProviderName = session.modelConfig?.providerName || ServiceProvider.DeepSeek;
    const allModels = useAllModels();
    const models = useMemo(() => {
        const filteredModels = allModels.filter((m) => m.available);
        const defaultModel = filteredModels.find((m) => m.isDefault);

        if (defaultModel) {
            const arr = [defaultModel, ...filteredModels.filter((m) => m !== defaultModel)];
            return arr;
        } else {
            return filteredModels;
        }
    }, [allModels]);
    // const currentModelName = useMemo(() => {
    //     const model = models.find(
    //         (m) => m.name == currentModel && m?.provider?.providerName == currentProviderName
    //     );
    //     return model?.displayName ?? "";
    // }, [models, currentModel, currentProviderName]);
    const [showModelSelector, setShowModelSelector] = useState(false);
    // const [showPluginSelector, setShowPluginSelector] = useState(false);
    // const [showUploadImage, setShowUploadImage] = useState(false);

    const [showSizeSelector, setShowSizeSelector] = useState(false);
    const [showQualitySelector, setShowQualitySelector] = useState(false);
    const [showStyleSelector, setShowStyleSelector] = useState(false);
    const modelSizes = getModelSizes(currentModel);
    const dalle3Qualitys: DalleQuality[] = ["standard", "hd"];
    const dalle3Styles: DalleStyle[] = ["vivid", "natural"];
    const currentSize = session.modelConfig?.size ?? ("1024x1024" as ModelSize);
    const currentQuality = session.modelConfig?.quality ?? "standard";
    const currentStyle = session.modelConfig?.style ?? "vivid";

    // 判断是不是移动端界面
    // const isMobileScreen = useMobileScreen();

    useEffect(() => {
        const show = isVisionModel(currentModel);
        // setShowUploadImage(show);
        if (!show) {
            props.setAttachImages([]);
            props.setUploading(false);
        }

        // if current model is not available
        // switch to first available model
        const isUnavailableModel = !models.some((m) => m.name === currentModel);
        if (isUnavailableModel && models.length > 0) {
            // show next model to default model if exist
            const nextModel = models.find((model) => model.isDefault) || models[0];
            chatStore.updateTargetSession(session, (session) => {
                session.modelConfig.model = nextModel.name;
                session.modelConfig.providerName = nextModel?.provider?.providerName as ServiceProvider;
            });
            showToast(nextModel?.provider?.providerName == "ByteDance" ? nextModel.displayName : nextModel.name);
        }
    }, [chatStore, currentModel, models, session]);

    return (
        <div className={styles["chat-input-actions"]}>
            <>
                {couldStop && <ChatAction onClick={stopAll} text={Locale.Chat.InputActions.Stop} icon={<StopIcon />} />}
                {/* 更换主题 */}
                <ChatAction
                    onClick={nextTheme}
                    text={Locale.Chat.InputActions.Theme[theme]}
                    icon={
                        <>
                            {theme === Theme.Auto ? (
                                <AutoIcon />
                            ) : theme === Theme.Light ? (
                                <LightIcon />
                            ) : theme === Theme.Dark ? (
                                <DarkIcon />
                            ) : null}
                        </>
                    }
                />
                {/* 快捷指令 用 / 唤起的*/}
                {/* <ChatAction
                    onClick={props.showPromptHints}
                    text={Locale.Chat.InputActions.Prompt}
                    icon={<PromptIcon />}
                /> */}

                {/* 清除聊天 */}
                {/* <ChatAction
                    text={Locale.Chat.InputActions.Clear}
                    icon={<BreakIcon />}
                    onClick={() => {
                        chatStore.updateTargetSession(session, (session) => {
                            if (session.clearContextIndex === session.messages.length) {
                                session.clearContextIndex = undefined;
                            } else {
                                session.clearContextIndex = session.messages.length;
                                session.memoryPrompt = ""; // will clear memory
                            }
                        });
                    }}
                /> */}

                {/* 查看键盘快捷方式 */}
                {showModelSelector && (
                    <Selector
                        defaultSelectedValue={`${currentModel}@${currentProviderName}`}
                        items={models.map((m) => ({
                            title: `${m.displayName}${
                                m?.provider?.providerName ? " (" + m?.provider?.providerName + ")" : ""
                            }`,
                            value: `${m.name}@${m?.provider?.providerName}`,
                        }))}
                        onClose={() => setShowModelSelector(false)}
                        onSelection={(s) => {
                            if (s.length === 0) return;
                            const [model, providerName] = getModelProvider(s[0]);
                            chatStore.updateTargetSession(session, (session) => {
                                session.modelConfig.model = model as ModelType;
                                session.modelConfig.providerName = providerName as ServiceProvider;
                            });
                            if (providerName == "ByteDance") {
                                const selectedModel = models.find(
                                    (m) => m.name == model && m?.provider?.providerName == providerName
                                );
                                showToast(selectedModel?.displayName ?? "");
                            } else {
                                showToast(model);
                            }
                        }}
                    />
                )}
                {/*  */}
                {supportsCustomSize(currentModel) && (
                    <ChatAction onClick={() => setShowSizeSelector(true)} text={currentSize} icon={<SizeIcon />} />
                )}
                {showSizeSelector && (
                    <Selector
                        defaultSelectedValue={currentSize}
                        items={modelSizes.map((m) => ({
                            title: m,
                            value: m,
                        }))}
                        onClose={() => setShowSizeSelector(false)}
                        onSelection={(s) => {
                            if (s.length === 0) return;
                            const size = s[0];
                            chatStore.updateTargetSession(session, (session) => {
                                session.modelConfig.size = size;
                            });
                            showToast(size);
                        }}
                    />
                )}

                {isDalle3(currentModel) && (
                    <ChatAction
                        onClick={() => setShowQualitySelector(true)}
                        text={currentQuality}
                        icon={<QualityIcon />}
                    />
                )}
                {showQualitySelector && (
                    <Selector
                        defaultSelectedValue={currentQuality}
                        items={dalle3Qualitys.map((m) => ({
                            title: m,
                            value: m,
                        }))}
                        onClose={() => setShowQualitySelector(false)}
                        onSelection={(q) => {
                            if (q.length === 0) return;
                            const quality = q[0];
                            chatStore.updateTargetSession(session, (session) => {
                                session.modelConfig.quality = quality;
                            });
                            showToast(quality);
                        }}
                    />
                )}
                {isDalle3(currentModel) && (
                    <ChatAction onClick={() => setShowStyleSelector(true)} text={currentStyle} icon={<StyleIcon />} />
                )}
                {showStyleSelector && (
                    <Selector
                        defaultSelectedValue={currentStyle}
                        items={dalle3Styles.map((m) => ({
                            title: m,
                            value: m,
                        }))}
                        onClose={() => setShowStyleSelector(false)}
                        onSelection={(s) => {
                            if (s.length === 0) return;
                            const style = s[0];
                            chatStore.updateTargetSession(session, (session) => {
                                session.modelConfig.style = style;
                            });
                            showToast(style);
                        }}
                    />
                )}
                {/* The following plugin functionality is commented out due to unresolved type errors after 'mask' removal. */}
                {/* Please update the path to the 'plugin' property if it has been moved elsewhere. */}
                {/* {showPlugins(currentProviderName, currentModel) && (
                    <ChatAction
                        onClick={() => {
                            if (pluginStore.getAll().length == 0) {
                                navigate(Path.Plugins);
                            } else {
                                setShowPluginSelector(true);
                            }
                        }}
                        text={Locale.Plugin.Name}
                        icon={<PluginIcon />}
                    />
                )}
                {showPluginSelector && (
                    <Selector
                        multiple
                        defaultSelectedValue={chatStore.currentSession().mask?.plugin}
                        items={pluginStore.getAll().map((item) => ({
                            title: `${item?.title}@${item?.version}`,
                            value: item?.id,
                        }))}
                        onClose={() => setShowPluginSelector(false)}
                        onSelection={(s) => {
                            chatStore.updateTargetSession(session, (session) => {
                                session.mask.plugin = s as string[];
                            });
                        }}
                    />
                )} */}
                {/* 键盘快捷方式 */}
                {/* {!isMobileScreen && (
                    <ChatAction
                        onClick={() => props.setShowShortcutKeyModal(true)}
                        text={Locale.Chat.ShortcutKey.Title}
                        icon={<ShortcutkeyIcon />}
                    />
                )} */}
            </>
            {/* <div className={styles["chat-input-actions-end"]}>
                {config.realtimeConfig.enable && (
                    <ChatAction
                        onClick={() => props.setShowChatSidePanel(true)}
                        text={"Realtime Chat"}
                        icon={<HeadphoneIcon />}
                    />
                )}
            </div> */}
        </div>
    );
}

export function EditMessageModal(props: { onClose: () => void }) {
    const chatStore = useChatStore();
    const session = chatStore.currentSession();
    const [messages, setMessages] = useState(session.messages.slice());

    return (
        <div className="modal-mask">
            <Modal
                title={Locale.Chat.EditMessage.Title}
                onClose={props.onClose}
                actions={[
                    <IconButton
                        text={Locale.UI.Cancel}
                        icon={<CancelIcon />}
                        key="cancel"
                        onClick={() => {
                            props.onClose();
                        }}
                    />,
                    <IconButton
                        type="primary"
                        text={Locale.UI.Confirm}
                        icon={<ConfirmIcon />}
                        key="ok"
                        onClick={() => {
                            chatStore.updateTargetSession(session, (session) => (session.messages = messages));
                            props.onClose();
                        }}
                    />,
                ]}
            >
                <List>
                    <ListItem
                        title={Locale.Chat.EditMessage.Topic.Title}
                        subTitle={Locale.Chat.EditMessage.Topic.SubTitle}
                    >
                        <input
                            type="text"
                            value={session.topic}
                            onInput={(e) =>
                                chatStore.updateTargetSession(
                                    session,
                                    (session) => (session.topic = e.currentTarget.value)
                                )
                            }
                        ></input>
                    </ListItem>
                </List>
            </Modal>
        </div>
    );
}

// 图片删除按钮
export function DeleteImageButton(props: { deleteImage: () => void }) {
    return (
        <div className={styles["delete-image"]} onClick={props.deleteImage}>
            <DeleteIcon />
        </div>
    );
}

// 设置的键盘快捷方式, 用于快速新建对话，聚焦输入框等
// export function ShortcutKeyModal(props: { onClose: () => void }) {
//     const isMac = navigator.platform.toUpperCase().indexOf("MAC") >= 0;
//     const shortcuts = [
//         {
//             title: Locale.Chat.ShortcutKey.newChat,
//             keys: isMac ? ["⌘", "Shift", "O"] : ["Ctrl", "Shift", "O"],
//         },
//         { title: Locale.Chat.ShortcutKey.focusInput, keys: ["Shift", "Esc"] },
//         {
//             title: Locale.Chat.ShortcutKey.copyLastCode,
//             keys: isMac ? ["⌘", "Shift", ";"] : ["Ctrl", "Shift", ";"],
//         },
//         {
//             title: Locale.Chat.ShortcutKey.copyLastMessage,
//             keys: isMac ? ["⌘", "Shift", "C"] : ["Ctrl", "Shift", "C"],
//         },
//         {
//             title: Locale.Chat.ShortcutKey.showShortcutKey,
//             keys: isMac ? ["⌘", "/"] : ["Ctrl", "/"],
//         },
//         {
//             title: Locale.Chat.ShortcutKey.clearContext,
//             keys: isMac ? ["⌘", "Shift", "backspace"] : ["Ctrl", "Shift", "backspace"],
//         },
//     ];
//     return (
//         <div className="modal-mask">
//             <Modal
//                 title={Locale.Chat.ShortcutKey.Title}
//                 onClose={props.onClose}
//                 actions={[
//                     <IconButton
//                         type="primary"
//                         text={Locale.UI.Confirm}
//                         icon={<ConfirmIcon />}
//                         key="ok"
//                         onClick={() => {
//                             props.onClose();
//                         }}
//                     />,
//                 ]}
//             >
//                 <div className={styles["shortcut-key-container"]}>
//                     <div className={styles["shortcut-key-grid"]}>
//                         {shortcuts.map((shortcut, index) => (
//                             <div key={index} className={styles["shortcut-key-item"]}>
//                                 <div className={styles["shortcut-key-title"]}>{shortcut.title}</div>
//                                 <div className={styles["shortcut-key-keys"]}>
//                                     {shortcut.keys.map((key, i) => (
//                                         <div key={i} className={styles["shortcut-key"]}>
//                                             <span>{key}</span>
//                                         </div>
//                                     ))}
//                                 </div>
//                             </div>
//                         ))}
//                     </div>
//                 </div>
//             </Modal>
//         </div>
//     );
// }

// 标题组件，从 Zustand store 获取并渲染
function TitleComponents() {
    const titleComponents = useTitleStore((state) => state.titleComponents);

    return (
        <>
            {Object.entries(titleComponents).map(([key, component]) => (
                <React.Fragment key={key}>{component}</React.Fragment>
            ))}
        </>
    );
}

function _Chat() {
    type RenderMessage = ChatMessage & { preview?: boolean };

    const chatStore = useChatStore();
    const session = chatStore.currentSession();
    const config = useAppConfig();

    const [showExport, setShowExport] = useState(false);

    const inputRef = useRef<HTMLTextAreaElement>(null);
    const [userInput, setUserInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const { submitKey, shouldSubmit } = useSubmitHandler();
    const scrollRef = useRef<HTMLDivElement>(null);
    const isScrolledToBottom = scrollRef?.current
        ? Math.abs(scrollRef.current.scrollHeight - (scrollRef.current.scrollTop + scrollRef.current.clientHeight)) <= 1
        : false;
    const isAttachWithTop = useMemo(() => {
        const lastMessage = scrollRef.current?.lastElementChild as HTMLElement;
        // if scrolllRef is not ready or no message, return false
        if (!scrollRef?.current || !lastMessage) return false;
        const topDistance = lastMessage!.getBoundingClientRect().top - scrollRef.current.getBoundingClientRect().top;
        // leave some space for user question
        return topDistance < 100;
    }, []);

    const isTyping = userInput !== "";

    // if user is typing, should auto scroll to bottom
    // if user is not typing, should auto scroll to bottom only if already at bottom
    const { setAutoScroll, scrollDomToBottom } = useScrollToBottom(
        scrollRef,
        (isScrolledToBottom || isAttachWithTop) && !isTyping,
        session.messages
    );
    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [hitBottom, setHitBottom] = useState(true);
    const isMobileScreen = useMobileScreen();
    const navigate = useNavigate();
    const [attachImages, setAttachImages] = useState<AttachedFile[]>([]);
    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [uploading, setUploading] = useState(false);

    // prompt hints
    const promptStore = usePromptStore();
    const [promptHints, setPromptHints] = useState<RenderPrompt[]>([]);
    const onSearch = useDebouncedCallback(
        (text: string) => {
            const matchedPrompts = promptStore.search(text);
            setPromptHints(matchedPrompts);
        },
        100,
        { leading: true, trailing: true }
    );

    // auto grow input
    const [inputRows, setInputRows] = useState(2);
    const measure = useDebouncedCallback(
        () => {
            const rows = inputRef.current ? autoGrowTextArea(inputRef.current) : 1;
            const inputRows = Math.min(20, Math.max(2 + Number(!isMobileScreen), rows));
            setInputRows(inputRows);
        },
        100,
        {
            leading: true,
            trailing: true,
        }
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
    useEffect(measure, [userInput]);

    // chat commands shortcuts
    const chatCommands = useChatCommand({
        new: () => chatStore.newSession(),
        newm: () => navigate(Path.NewChat),
        prev: () => chatStore.nextSession(-1),
        next: () => chatStore.nextSession(1),
        clear: () =>
            chatStore.updateTargetSession(session, (session) => (session.clearContextIndex = session.messages.length)),
        fork: () => chatStore.forkSession(),
        del: () => chatStore.deleteSession(chatStore.currentSessionIndex),
    });

    // only search prompts when user input is short
    const SEARCH_TEXT_LIMIT = 30;
    const onInput = (text: string) => {
        setUserInput(text);
        const n = text.trim().length;

        // clear search results
        if (n === 0) {
            setPromptHints([]);
        } else if (text.match(ChatCommandPrefix)) {
            setPromptHints(chatCommands.search(text));
        } else if (!config.disablePromptHint && n < SEARCH_TEXT_LIMIT) {
            // check if need to trigger auto completion
            if (text.startsWith("/")) {
                const searchText = text.slice(1);
                onSearch(searchText);
            }
        }
    };

    // 要想理解这块，需要先看store里的chat.ts，后续后端支持数据库的话就把不需要本地存储的移除:-)
    const doSubmit = async (userInput: string) => {
        message.info("暂不开放对话功能");
        return;
        // if (userInput.trim() === "" && isEmpty(attachImages)) return;
        // const matchCommand = chatCommands.match(userInput);
        // if (matchCommand.matched) {
        //     setUserInput("");
        //     setPromptHints([]);
        //     matchCommand.invoke();
        //     return;
        // }

        // // 先保存用户输入，然后立即清空输入框
        // const inputText = userInput;
        // setUserInput("");
        // setPromptHints([]);

        // setIsLoading(true);

        // try {
        //     // 先创建用户消息并添加到对话中
        //     const userMessage = createMessage({
        //         role: "user",
        //         content: inputText,
        //     });

        //     // 添加用户消息到当前会话
        //     chatStore.updateTargetSession(session, (session) => {
        //         session.messages.push(userMessage);
        //     });

        //     // 使用相对路径请求，避免跨域问题`
        //     const response = await fetch("/api/chat-proxy", {
        //         method: "POST",
        //         headers: {
        //             "Content-Type": "application/json",
        //         },
        //         body: JSON.stringify({
        //             userQuery: inputText,
        //             metadata: {
        //                 enableThinking: false,
        //             },
        //         }),
        //     });

        //     // 检查响应
        //     if (!response.ok) {
        //         throw new Error(`请求失败: ${response.status}`);
        //     }

        //     // 创建新的消息
        //     const newMessage = createMessage({
        //         role: "assistant",
        //         content: "",
        //     });

        //     // 添加消息到当前会话
        //     chatStore.updateTargetSession(session, (session) => {
        //         session.messages.push(newMessage);
        //     });

        //     // 处理返回的数据
        //     const reader = response.body?.getReader();
        //     if (reader) {
        //         let jsonContent = ""; // 存储JSON内容（resume_cards类型）
        //         let textContent = ""; // 存储富文本内容（text类型）

        //         // TODO: 先这样写，后面更改，先通过husky的检查
        //         let isDone = false;
        //         while (!isDone) {
        //             const { done, value } = await reader.read();
        //             if (done) {
        //                 isDone = true;
        //                 break;
        //             }

        //             // 解码数据
        //             const text = new TextDecoder().decode(value);

        //             // 处理SSE格式数据（移除data:前缀并解析JSON）
        //             const lines = text.trim().split("\n");

        //             for (const line of lines) {
        //                 if (line.startsWith("data:")) {
        //                     const jsonStr = line.substring(5); // 移除"data:"前缀

        //                     try {
        //                         const data = JSON.parse(jsonStr);
        //                         const { type, content } = data;

        //                         if (type === "resume_cards") {
        //                             // 存储JSON内容
        //                             jsonContent = content;

        //                             // 更新消息内容，只有JSON部分
        //                             chatStore.updateTargetSession(session, (session) => {
        //                                 const lastMessage = session.messages[session.messages.length - 1];
        //                                 if (lastMessage.id === newMessage.id) {
        //                                     lastMessage.content = jsonContent;
        //                                 }
        //                             });
        //                         } else if (type === "text") {
        //                             // 累积富文本内容
        //                             textContent += content;

        //                             // 更新消息内容，JSON + 当前累积的富文本
        //                             const finalContent = jsonContent
        //                                 ? jsonContent + "\n\n" + textContent.trim()
        //                                 : textContent.trim();

        //                             chatStore.updateTargetSession(session, (session) => {
        //                                 const lastMessage = session.messages[session.messages.length - 1];
        //                                 if (lastMessage.id === newMessage.id) {
        //                                     lastMessage.content = finalContent;
        //                                 }
        //                             });
        //                         }
        //                     } catch (e) {
        //                         console.error("JSON解析错误:", e, "原始数据:", jsonStr);
        //                         // 如果JSON解析出错，将原始内容当作文本处理
        //                         textContent += jsonStr;

        //                         const finalContent = jsonContent
        //                             ? jsonContent + "\n\n" + textContent.trim()
        //                             : textContent.trim();

        //                         chatStore.updateTargetSession(session, (session) => {
        //                             const lastMessage = session.messages[session.messages.length - 1];
        //                             if (lastMessage.id === newMessage.id) {
        //                                 lastMessage.content = finalContent;
        //                             }
        //                         });
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // } catch (error: any) {
        //     console.error("API调用错误:", error);
        //     // 显示错误消息
        //     const errorMessage = createMessage({
        //         role: "assistant",
        //         content: `发生错误: ${error.message}`,
        //         isError: true,
        //     });

        //     chatStore.updateTargetSession(session, (session) => {
        //         session.messages.push(errorMessage);
        //     });
        // } finally {
        //     setIsLoading(false);
        //     setAttachImages([]);
        //     chatStore.setLastInput(inputText);
        //     if (!isMobileScreen) inputRef.current?.focus();
        //     setAutoScroll(true);
        // }
    };

    const onPromptSelect = (prompt: RenderPrompt) => {
        setTimeout(() => {
            setPromptHints([]);

            const matchedChatCommand = chatCommands.match(prompt.content);
            if (matchedChatCommand.matched) {
                // if user is selecting a chat command, just trigger it
                matchedChatCommand.invoke();
                setUserInput("");
            } else {
                // or fill the prompt
                setUserInput(prompt.content);
            }
            inputRef.current?.focus();
        }, 30);
    };

    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const onUserStop = (messageId: string) => {
        ChatControllerPool.stop(session.id, messageId);
    };

    useEffect(() => {
        chatStore.updateTargetSession(session, (session) => {
            const stopTiming = Date.now() - REQUEST_TIMEOUT_MS;
            session.messages.forEach((m) => {
                // check if should stop all stale messages
                if (m.isError || new Date(m.date).getTime() < stopTiming) {
                    if (m.streaming) {
                        m.streaming = false;
                    }

                    if (m.content.length === 0) {
                        m.isError = true;
                        m.content = prettyObject({
                            error: true,
                            message: "empty response",
                        });
                    }
                }
            });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [session]);

    // check if should send message
    const onInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        // if ArrowUp and no userInput, fill with last input
        if (e.key === "ArrowUp" && userInput.length <= 0 && !(e.metaKey || e.altKey || e.ctrlKey)) {
            setUserInput(chatStore.lastInput ?? "");
            e.preventDefault();
            return;
        }
        if (shouldSubmit(e) && promptHints.length === 0) {
            doSubmit(userInput);
            e.preventDefault();
        }
    };

    const deleteMessage = (msgId?: string) => {
        chatStore.updateTargetSession(
            session,
            (session) => (session.messages = session.messages.filter((m) => m.id !== msgId))
        );
    };

    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const onDelete = (msgId: string) => {
        deleteMessage(msgId);
    };

    // TODO 重发消息，后面还要用于重发功能，还没做
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const onResend = (message: ChatMessage) => {
        // when it is resending a message
        // 1. for a user's message, find the next bot response
        // 2. for a bot's message, find the last user's input
        // 3. delete original user input and bot's message
        // 4. resend the user's input

        const resendingIndex = session.messages.findIndex((m) => m.id === message.id);

        if (resendingIndex < 0 || resendingIndex >= session.messages.length) {
            console.error("[Chat] failed to find resending message", message);
            return;
        }

        let userMessage: ChatMessage | undefined;
        let botMessage: ChatMessage | undefined;

        if (message.role === "assistant") {
            // if it is resending a bot's message, find the user input for it
            botMessage = message;
            for (let i = resendingIndex; i >= 0; i -= 1) {
                if (session.messages[i].role === "user") {
                    userMessage = session.messages[i];
                    break;
                }
            }
        } else if (message.role === "user") {
            // if it is resending a user's input, find the bot's response
            userMessage = message;
            for (let i = resendingIndex; i < session.messages.length; i += 1) {
                if (session.messages[i].role === "assistant") {
                    botMessage = session.messages[i];
                    break;
                }
            }
        }

        if (userMessage === undefined) {
            console.error("[Chat] failed to resend", message);
            return;
        }

        // delete the original messages
        deleteMessage(userMessage.id);
        deleteMessage(botMessage?.id);

        // resend the message
        setIsLoading(true);
        const textContent = getMessageTextContent(userMessage);
        const images = getMessageImages(userMessage);
        chatStore.onUserInput(textContent, images).then(() => setIsLoading(false));
        inputRef.current?.focus();
    };

    const accessStore = useAccessStore();
    const [speechStatus, setSpeechStatus] = useState(false);
    // const [speechLoading, setSpeechLoading] = useState(false);

    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async function openaiSpeech(text: string) {
        if (speechStatus) {
            ttsPlayer.stop();
            setSpeechStatus(false);
        } else {
            let api: ClientApi;
            // eslint-disable-next-line prefer-const
            api = new ClientApi(ModelProvider.DeepSeek);
            const config = useAppConfig.getState();
            // setSpeechLoading(true);
            ttsPlayer.init();
            let audioBuffer: ArrayBuffer;
            const textContent = markdownToTxt(text);
            if (config.ttsConfig.engine !== DEFAULT_TTS_ENGINE) {
                const edgeVoiceName = accessStore.edgeVoiceName();
                const tts = new MsEdgeTTS();
                await tts.setMetadata(edgeVoiceName, OUTPUT_FORMAT.AUDIO_24KHZ_96KBITRATE_MONO_MP3);
                audioBuffer = await tts.toArrayBuffer(textContent);
            } else {
                audioBuffer = await api.llm.speech({
                    model: config.ttsConfig.model,
                    input: textContent,
                    voice: config.ttsConfig.voice,
                    speed: config.ttsConfig.speed,
                });
            }
            setSpeechStatus(true);
            ttsPlayer
                .play(audioBuffer, () => {
                    setSpeechStatus(false);
                })
                .catch((e) => {
                    console.error("[OpenAI Speech]", e);
                    showToast(prettyObject(e));
                    setSpeechStatus(false);
                });
            // .finally(() => setSpeechLoading(false));
        }
    }

    const context: RenderMessage[] = useMemo(() => {
        return session.context?.slice() ?? [];
    }, [session.context]);
    // preview messages
    const renderMessages = useMemo(() => {
        return context.concat(session.messages as RenderMessage[]).concat(
            isLoading
                ? [
                      {
                          ...createMessage({
                              role: "assistant",
                              content: "正在思考中",
                          }),
                          preview: true,
                      },
                  ]
                : []
        );
    }, [context, isLoading, session.messages]);

    const [msgRenderIndex, _setMsgRenderIndex] = useState(Math.max(0, renderMessages.length - CHAT_PAGE_SIZE));

    function setMsgRenderIndex(newIndex: number) {
        newIndex = Math.min(renderMessages.length - CHAT_PAGE_SIZE, newIndex);
        newIndex = Math.max(0, newIndex);
        _setMsgRenderIndex(newIndex);
    }

    const messages = useMemo(() => {
        const endRenderIndex = Math.min(msgRenderIndex + 3 * CHAT_PAGE_SIZE, renderMessages.length);
        return renderMessages.slice(msgRenderIndex, endRenderIndex);
    }, [msgRenderIndex, renderMessages]);

    // 滚动条事件
    const onChatBodyScroll = (e: HTMLElement) => {
        const bottomHeight = e.scrollTop + e.clientHeight;
        const edgeThreshold = e.clientHeight;

        const isTouchTopEdge = e.scrollTop <= edgeThreshold;
        const isTouchBottomEdge = bottomHeight >= e.scrollHeight - edgeThreshold;
        const isHitBottom = bottomHeight >= e.scrollHeight - (isMobileScreen ? 4 : 10);

        const prevPageMsgIndex = msgRenderIndex - CHAT_PAGE_SIZE;
        const nextPageMsgIndex = msgRenderIndex + CHAT_PAGE_SIZE;

        if (isTouchTopEdge && !isTouchBottomEdge) {
            setMsgRenderIndex(prevPageMsgIndex);
        } else if (isTouchBottomEdge) {
            setMsgRenderIndex(nextPageMsgIndex);
        }

        setHitBottom(isHitBottom);
        setAutoScroll(isHitBottom);
    };

    // 滚到底部
    function scrollToBottom() {
        setMsgRenderIndex(renderMessages.length - CHAT_PAGE_SIZE);
        scrollDomToBottom();
    }

    // 清除上下文索引
    const clearContextIndex =
        (session.clearContextIndex ?? -1) >= 0 ? session.clearContextIndex! + context.length - msgRenderIndex : -1;

    // const clientConfig = useMemo(() => getClientConfig(), []);

    const autoFocus = !isMobileScreen; // wont auto focus on mobile screen
    // const showMaxIcon = !isMobileScreen && !clientConfig?.isApp;

    useCommand({
        fill: setUserInput,
        submit: (text) => {
            doSubmit(text);
        },
        code: (text) => {
            if (accessStore.disableFastLink) return;
            console.log("[Command] got code from url: ", text);
            showConfirm(Locale.URLCommand.Code + `code = ${text}`).then((res) => {
                if (res) {
                    accessStore.update((access) => (access.accessCode = text));
                }
            });
        },
        settings: (text) => {
            if (accessStore.disableFastLink) return;

            try {
                const payload = JSON.parse(text) as {
                    key?: string;
                    url?: string;
                };

                console.log("[Command] got settings from url: ", payload);

                if (payload.key || payload.url) {
                    showConfirm(Locale.URLCommand.Settings + `\n${JSON.stringify(payload, null, 4)}`).then((res) => {
                        if (!res) return;
                        if (payload.key) {
                            // accessStore.update((access) => (access.openaiApiKey = payload.key!));
                        }
                        if (payload.url) {
                            // accessStore.update((access) => (access.openaiUrl = payload.url!));
                        }
                        accessStore.update((access) => (access.useCustomConfig = true));
                    });
                }
            } catch {
                console.error("[Command] failed to get settings from url: ", text);
            }
        },
    });

    // edit / insert message modal
    const [isEditingMessage, setIsEditingMessage] = useState(false);

    // remember unfinished input
    useEffect(() => {
        // try to load from local storage
        const key = UNFINISHED_INPUT(session.id);
        const mayBeUnfinishedInput = localStorage.getItem(key);
        if (mayBeUnfinishedInput && userInput.length === 0) {
            setUserInput(mayBeUnfinishedInput);
            localStorage.removeItem(key);
        }

        const dom = inputRef.current;
        return () => {
            localStorage.setItem(key, dom?.value ?? "");
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handlePaste = useCallback(
        async (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
            const currentModel = chatStore.currentSession().modelConfig.model;
            if (!isVisionModel(currentModel)) {
                return;
            }
            const items = (event.clipboardData || window.clipboardData).items;
            for (const item of items) {
                if (item.kind === "file" && item.type.startsWith("image/")) {
                    event.preventDefault();
                    const file = item.getAsFile();
                    if (file) {
                        const images: string[] = [];
                        // images.push(...attachImages);
                        images.push(
                            ...(await new Promise<string[]>((res, rej) => {
                                setUploading(true);
                                const imagesData: string[] = [];
                                uploadImageRemote(file)
                                    .then((dataUrl) => {
                                        imagesData.push(dataUrl);
                                        setUploading(false);
                                        res(imagesData);
                                    })
                                    .catch((e) => {
                                        setUploading(false);
                                        rej(e);
                                    });
                            }))
                        );
                        const imagesLength = images.length;

                        if (imagesLength > 3) {
                            images.splice(3, imagesLength - 3);
                        }
                        // setAttachImages(images);
                    }
                }
            }
        },
        [attachImages, chatStore]
    );

    // TODO
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    // async function uploadImage() {
    //     const images: string[] = [];
    //     images.push(...attachImages);

    //     images.push(
    //         ...(await new Promise<string[]>((res, rej) => {
    //             const fileInput = document.createElement("input");
    //             fileInput.type = "file";
    //             fileInput.accept = "image/png, image/jpeg, image/webp, image/heic, image/heif";
    //             fileInput.multiple = true;
    //             fileInput.onchange = (event: any) => {
    //                 setUploading(true);
    //                 const files = event.target.files;
    //                 const imagesData: string[] = [];
    //                 for (let i = 0; i < files.length; i++) {
    //                     const file = event.target.files[i];
    //                     uploadImageRemote(file)
    //                         .then((dataUrl) => {
    //                             imagesData.push(dataUrl);
    //                             if (imagesData.length === 3 || imagesData.length === files.length) {
    //                                 setUploading(false);
    //                                 res(imagesData);
    //                             }
    //                         })
    //                         .catch((e) => {
    //                             setUploading(false);
    //                             rej(e);
    //                         });
    //                 }
    //             };
    //             fileInput.click();
    //         }))
    //     );

    //     const imagesLength = images.length;
    //     if (imagesLength > 3) {
    //         images.splice(3, imagesLength - 3);
    //     }
    //     setAttachImages(images);
    // }

    // 快捷键 shortcut keys
    const [showShortcutKeyModal, setShowShortcutKeyModal] = useState(false);

    // useEffect(() => {
    //     const handleKeyDown = (event: KeyboardEvent) => {
    //         // 打开新聊天 command + shift + o
    //         if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === "o") {
    //             event.preventDefault();
    //             setTimeout(() => {
    //                 chatStore.newSession();
    //                 navigate(Path.Chat);
    //             }, 10);
    //         }
    //         // 聚焦聊天输入 shift + esc
    //         else if (event.shiftKey && event.key.toLowerCase() === "escape") {
    //             event.preventDefault();
    //             inputRef.current?.focus();
    //         }
    //         // 复制最后一个代码块 command + shift + ;
    //         else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.code === "Semicolon") {
    //             event.preventDefault();
    //             const copyCodeButton = document.querySelectorAll<HTMLElement>(".copy-code-button");
    //             if (copyCodeButton.length > 0) {
    //                 copyCodeButton[copyCodeButton.length - 1].click();
    //             }
    //         }
    //         // 复制最后一个回复 command + shift + c
    //         else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === "c") {
    //             event.preventDefault();
    //             const lastNonUserMessage = messages.filter((message) => message.role !== "user").pop();
    //             if (lastNonUserMessage) {
    //                 const lastMessageContent = getMessageTextContent(lastNonUserMessage);
    //                 copyToClipboard(lastMessageContent);
    //             }
    //         }
    //         // 展示快捷键 command + /
    //         else if ((event.metaKey || event.ctrlKey) && event.key === "/") {
    //             event.preventDefault();
    //             setShowShortcutKeyModal(true);
    //         }
    //         // 清除上下文 command + shift + backspace
    //         else if ((event.metaKey || event.ctrlKey) && event.shiftKey && event.key.toLowerCase() === "backspace") {
    //             event.preventDefault();
    //             chatStore.updateTargetSession(session, (session) => {
    //                 if (session.clearContextIndex === session.messages.length) {
    //                     session.clearContextIndex = undefined;
    //                 } else {
    //                     session.clearContextIndex = session.messages.length;
    //                     session.memoryPrompt = ""; // will clear memory
    //                 }
    //             });
    //         }
    //     };

    //     document.addEventListener("keydown", handleKeyDown);

    //     return () => {
    //         document.removeEventListener("keydown", handleKeyDown);
    //     };
    // }, [messages, chatStore, navigate, session]);

    // const [showChatSidePanel, setShowChatSidePanel] = useState(false);

    return (
        <>
            <div className={styles.chat} key={session.id}>
                <div className={styles["chat-header"]} data-tauri-drag-region>
                    <div className={styles["chat-body-title"]}>
                        <TitleComponents />
                        <div
                            className={styles["chat-body-main-title"]}
                            // onClickCapture={() => setIsEditingMessage(true)}
                        >
                            {!session.topic ? DEFAULT_TOPIC : session.topic}
                        </div>
                        {/* 对话顶部的相关内容 */}
                    </div>
                </div>
                <div className={styles["chat-main"]}>
                    {/* 聊天界面主容器，包含消息列表和输入框 */}
                    <div className={styles["chat-body-container"]}>
                        {/* 聊天消息列表容器 */}
                        <div
                            className={styles["chat-body"]}
                            ref={scrollRef} // 滚动引用，用于控制滚动位置
                            onScroll={(e) => onChatBodyScroll(e.currentTarget)} // 监听滚动事件以实现无限滚动和滚动到底部功能
                            onMouseDown={() => inputRef.current?.blur()} // 点击聊天区域时取消输入框焦点
                            onTouchStart={() => {
                                inputRef.current?.blur();
                                setAutoScroll(false); // 触摸开始时取消自动滚动
                            }}
                        >
                            {/* 遍历并渲染所有消息 */}
                            {messages.map((message, i) => {
                                const isUser = message.role === "user"; // 判断是否为用户消息
                                // const isContext = i < context.length;
                                // TODO
                                const showTyping = message.preview || message.streaming; // 判断是否显示"正在输入"状态

                                const shouldShowClearContextDivider = i === clearContextIndex - 1; // 判断是否显示上下文清除分隔符

                                return (
                                    <Fragment key={message.id}>
                                        {/* 消息项容器，根据发送者使用不同样式 */}
                                        <div
                                            className={
                                                isUser
                                                    ? styles["chat-message-user"] // 用户消息样式
                                                    : styles["chat-message"] // AI消息样式
                                            }
                                        >
                                            <div className={styles["chat-message-container"]}>
                                                {/* 消息头部，包含头像和模型信息 */}
                                                <div className={styles["chat-message-header"]}>
                                                    <div className={styles["chat-message-avatar"]}>
                                                        {/* 根据消息角色显示不同头像 */}
                                                        {isUser ? null : (
                                                            <Avatar
                                                                icon={<ChatIcon />}
                                                                size={28}
                                                                style={{
                                                                    border: "1px solid rgba(0, 153, 242, 0.30)",
                                                                    background: "#e8f8fc",
                                                                }}
                                                            ></Avatar>
                                                        )}
                                                    </div>
                                                </div>
                                                {/* 正在输入状态显示 */}
                                                {message?.tools?.length == 0 && showTyping && (
                                                    <div className={styles["chat-message-status"]}>
                                                        {Locale.Chat.Typing}
                                                    </div>
                                                )}

                                                {/* 消息内容展示区域 */}
                                                <div
                                                    className={
                                                        isUser
                                                            ? styles["message-user-content-wrapper"]
                                                            : styles["message-user-ai-wrapper"]
                                                    }
                                                >
                                                    <div
                                                        className={
                                                            isUser
                                                                ? styles["chat-message-item-user"]
                                                                : styles["chat-message-item-ai"]
                                                        }
                                                    >
                                                        {/* 根据用户角色使用不同的Markdown渲染器 */}
                                                        {isUser ? (
                                                            getMessageTextContent(message)
                                                        ) : (
                                                            <MDXMarkdown
                                                                key={message.streaming ? "loading" : "done"}
                                                                content={getMessageTextContent(message)}
                                                                loading={
                                                                    (message.preview || message.streaming) &&
                                                                    message.content.length === 0 &&
                                                                    !isUser
                                                                }
                                                                fontSize={16}
                                                                fontFamily={"PingFang SC"}
                                                                parentRef={scrollRef}
                                                                defaultShow={i >= messages.length - 6}
                                                            />
                                                        )}

                                                        {/* 单张图片展示 */}
                                                        {getMessageImages(message).length == 1 && (
                                                            // eslint-disable-next-line @next/next/no-img-element
                                                            <img
                                                                className={styles["chat-message-item-image"]}
                                                                src={getMessageImages(message)[0]}
                                                                alt=""
                                                            />
                                                        )}
                                                        {/* 多张图片展示 */}
                                                        {getMessageImages(message).length > 1 && (
                                                            <div
                                                                className={styles["chat-message-item-images"]}
                                                                style={
                                                                    {
                                                                        "--image-count":
                                                                            getMessageImages(message).length, // CSS变量，控制图片网格布局
                                                                    } as React.CSSProperties
                                                                }
                                                            >
                                                                {getMessageImages(message).map((image, index) => {
                                                                    return (
                                                                        // eslint-disable-next-line @next/next/no-img-element
                                                                        <img
                                                                            className={
                                                                                styles["chat-message-item-image-multi"]
                                                                            }
                                                                            key={index}
                                                                            src={image}
                                                                            alt=""
                                                                        />
                                                                    );
                                                                })}
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className={styles["btn-group"]}>
                                                        {isUser && (
                                                            <Button
                                                                icon={<EditIcon />}
                                                                type="text"
                                                                iconPosition={"start"}
                                                                onClick={() =>
                                                                    copyToClipboard(getMessageTextContent(message))
                                                                }
                                                            >
                                                                编辑
                                                            </Button>
                                                        )}
                                                        <Button
                                                            icon={<CopyIcon />}
                                                            type="text"
                                                            iconPosition={"start"}
                                                            onClick={() =>
                                                                copyToClipboard(getMessageTextContent(message))
                                                            }
                                                        >
                                                            复制
                                                        </Button>
                                                        <Button icon={<RetryIcon />} type="text" iconPosition={"start"}>
                                                            重试
                                                        </Button>

                                                        {!isUser && (
                                                            <>
                                                                <Button
                                                                    icon={<ShareIcon />}
                                                                    type="text"
                                                                    iconPosition={"start"}
                                                                >
                                                                    分享
                                                                </Button>
                                                                <Divider type="vertical" style={{ height: "14px" }} />
                                                                <Button
                                                                    icon={<StarIcon />}
                                                                    type="text"
                                                                    iconPosition={"start"}
                                                                ></Button>
                                                                <Button
                                                                    icon={<LikeIcon />}
                                                                    type="text"
                                                                    iconPosition={"start"}
                                                                ></Button>
                                                                <Button
                                                                    icon={<DislikeIcon />}
                                                                    type="text"
                                                                    iconPosition={"start"}
                                                                ></Button>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>

                                                {/* 音频消息播放器 */}
                                                {message?.audio_url && (
                                                    <div className={styles["chat-message-audio"]}>
                                                        <audio src={message.audio_url} controls />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        {/* 清除上下文分隔线 */}
                                        {shouldShowClearContextDivider && <ClearContextDivider />}
                                    </Fragment>
                                );
                            })}
                        </div>
                        {/* 聊天输入面板 */}
                        <div className={styles["chat-input-panel"]}>
                            {/* 提示建议显示区域 */}
                            <PromptHints prompts={promptHints} onPromptSelect={onPromptSelect} />

                            {/* 聊天操作按钮组件 */}
                            {/* <ChatActions
                                uploadImage={uploadImage}
                                setAttachImages={setAttachImages}
                                setUploading={setUploading}
                                scrollToBottom={scrollToBottom}
                                hitBottom={hitBottom}
                                uploading={uploading}
                                showPromptHints={() => {
                                    // 再次点击关闭提示
                                    if (promptHints.length > 0) {
                                        setPromptHints([]);
                                        return;
                                    }

                                    inputRef.current?.focus();
                                    setUserInput("/");
                                    onSearch("");
                                }}
                                setShowShortcutKeyModal={setShowShortcutKeyModal}
                                setUserInput={setUserInput}
                                // setShowChatSidePanel={setShowChatSidePanel}
                            /> */}

                            {/* 聊天输入组件 */}
                            <ChatInput
                                inputRef={inputRef} // 输入框引用
                                userInput={userInput} // 输入内容
                                onUserInput={onInput} // 输入变化处理函数
                                onKeyDown={onInputKeyDown} // 键盘事件处理
                                onPaste={handlePaste} // 粘贴处理（用于处理图片粘贴）
                                onSubmit={() => doSubmit(userInput)} // 提交处理
                                attachImages={attachImages} // 附加图片数组
                                setAttachImages={setAttachImages} // 设置附加图片
                                inputRows={inputRows} // 输入框行数（自适应高度）
                                placeholder={Locale.Chat.Input(submitKey)} // 占位文本
                                sendButtonText={Locale.Chat.Send} // 发送按钮文本
                                fontSize={config.fontSize} // 字体大小
                                fontFamily={config.fontFamily} // 字体系列
                                autoFocus={autoFocus} // 自动聚焦
                                scrollToBottom={scrollToBottom} // 滚动到底部方法
                                isLoading={isLoading}
                            />
                        </div>
                    </div>
                    {/* 聊天侧边面板（已注释掉，用于实时语音交流） */}
                    {/* <div
                        className={clsx(styles["chat-side-panel"], {
                            [styles["mobile"]]: isMobileScreen,
                            [styles["chat-side-panel-show"]]: showChatSidePanel,
                        })}
                    >
                        {showChatSidePanel && (
                            <RealtimeChat
                                onClose={() => {
                                    setShowChatSidePanel(false);
                                }}
                                onStartVoice={async () => {
                                    console.log("start voice");
                                }}
                            />
                        )}
                    </div> */}
                </div>

                {!hitBottom && (
                    <FloatButton
                        icon={<ToBottomIcon />}
                        onClick={scrollToBottom}
                        style={{ position: "absolute", right: "48.8%", bottom: "28.7%" }}
                    />
                )}
            </div>
            {showExport && <ExportMessageModal onClose={() => setShowExport(false)} />}

            {isEditingMessage && (
                <EditMessageModal
                    onClose={() => {
                        setIsEditingMessage(false);
                    }}
                />
            )}

            {/* {showShortcutKeyModal && <ShortcutKeyModal onClose={() => setShowShortcutKeyModal(false)} />} */}
        </>
    );
}

export function Chat() {
    const chatStore = useChatStore();
    const session = chatStore.currentSession();
    return <_Chat key={session.id}></_Chat>;
}
