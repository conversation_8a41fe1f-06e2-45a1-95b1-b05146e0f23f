import { <PERSON><PERSON>, <PERSON><PERSON>, Button, Col, Divider, Empty, Flex, Layout, Modal, Popover, Rate, Row, Tabs, Tag } from "antd";
import { ArrowLeftOutlined, ArrowRightOutlined, CloseOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import Header from "@/app/components/Header";
import styles from "./index.module.scss";
import Tags from "@/app/components/Tags";
import StandardResume from "./StandardResume";
import ProfileFit from "@/app/components/ProfileFit";
import { useEffect, useMemo, useRef, useState } from "react";
import InfoFieldGroup from "@/app/components/InfoFieldGroup";
import InfoField from "@/app/components/InfoField";
import AiSuggestionPrefix from "@/app/icons/talentTool/ai-suggestion-prefix.svg";
import EducationExperienceIcon from "@/app/icons/talentTool/education-experience.svg";
import WorkExperienceIcon from "@/app/icons/talentTool/work-experience.svg";
import CopyIcon from "@/app/icons/talentTool/copy.svg";
import { useLocation } from "react-router-dom";
import { getFileApi } from "@/app/request/modules/common";
import {
    getBeisenEvaluationReportApi,
    getInterviewFlowApi,
    getResumeDetailApi,
    getSimpleCandidateListApi,
    startInterviewApi,
} from "@/app/request/modules/candidate";
import PreviewFile from "@/app/components/PreviewFile";
import { PreviewFileExpose } from "@/app/components/typing";
import InterviewEvaluation from "@/app/views/Candidate/InterviewEvaluation";
import UseCandidateStore, {
    AddJobExpose,
    CandidateApplicationResp,
    CandidateListResp,
    ChangeStatusExpose,
    InterviewEvaluationExpose,
    InterviewFlowResp,
    InterviewTimeResp,
    TrackStageStatistic,
} from "@/app/store/modules/candidate";
import { calculateAge, formatTime } from "@/app/utils";
import RenderFile from "@/app/components/RenderFile";
import { candidateNextStageApi, getInterviewTranscriptApi } from "@/app/request/modules/candidate";
import { RespPaginationParams, RespParams } from "@/app/typing";
import messageService from "@/app/lib/message";
import { getAllTagsApi } from "@/app/request/modules/dashboard";
import { EVALUATE_TYPE, JOB_TYPE } from "@/app/constant";
import ChangeStatus from "./ChangeStatus";
import ChatDialog from "@/app/components/ChatDialog";
import Link from "antd/lib/typography/Link";
import OperationLogs from "./OperationLogs";
import AddJob from "./AddJob";
import InterviewTranscript from "./InterviewTranscript";
import useCandidateDetailStore from "@/app/store/modules/candidate/detail";
import EvaluationReport from "./EvaluationReport";
import { copyToClipboard } from "@/app/utils";
import { InterviewTranscriptReq, InterviewTranscriptResp } from "@/app/store/modules/candidate";
import AIInterviewQuestion from "./AIInterviewQuestion";
type SimpleCandidateListResp = Pick<CandidateApplicationResp, "applicantId" | "jobId">;

const TalentDetail = () => {
    const location = useLocation();
    const [detailInfo, setDetailInfo] = useState<CandidateListResp | null>(null);
    const [showPopover, setShowPopover] = useState(false);
    const [resumeBlob, setResumeBlob] = useState<Blob | null>(null);
    const [originType, setOriginType] = useState<string>("");
    const [isManualDistribution, setIsManualDistribution] = useState<boolean>(false);
    const [simpleCandidateList, setSimpleCandidateList] = useState<SimpleCandidateListResp[]>([]);
    // 附件数据
    const [attachments, setAttachments] = useState(["aaaaaaaaabbbbbbbcccccccc", "12312312355555566666777"]);
    const [showInterviewInfo, setShowInterviewInfo] = useState<boolean>(false);
    const [interviewInfo, setInterviewInfo] = useState<InterviewTimeResp>();
    const [interviewTranscriptData, setInterviewTranscriptData] = useState<InterviewTranscriptResp[]>([]);
    const [hasInterviewTranscript, setHasInterviewTranscript] = useState<boolean>(false);
    const [loadingInterviewTranscript, setLoadingInterviewTranscript] = useState<boolean>(false);
    const { statusList, allTags, setStatusList, setAllTags } = UseCandidateStore((state) => ({
        statusList: state.statusList,
        allTags: state.allTags,
        setStatusList: state.setStatusList,
        setAllTags: state.setAllTags,
    }));
    const { jobId, applicantId, setJobId, setApplicantId } = useCandidateDetailStore((state) => ({
        jobId: state.jobId,
        applicantId: state.applicantId,
        setJobId: state.setJobId,
        setApplicantId: state.setApplicantId,
    }));

    const previewFileRef = useRef<PreviewFileExpose>(null);
    const interviewEvaluationRef = useRef<InterviewEvaluationExpose>(null);
    const changeStatusRef = useRef<ChangeStatusExpose>(null);
    const addJobRef = useRef<AddJobExpose>(null);
    // 获取所有标签tags
    const getAllTags = async () => {
        const res: any = await (await getAllTagsApi({ label: "", layer: "" })).json();
        if (res.code === 200) {
            setAllTags(res.data);
        }
    };
    // 获取面试流程列表
    const getInterviewFlowList = async () => {
        try {
            const res: RespParams<InterviewFlowResp> = await (await getInterviewFlowApi(Number(jobId))).json();
            if (res.code === 200) {
                const list: TrackStageStatistic[] = res.data.trackStageStatistics;
                setStatusList(list);
            }
        } catch (error) {
            setStatusList([]);
        }
    };
    // 获取简历详情数据
    const getResumeDetail = async (jobId: string | null, applicantId: string | null) => {
        if (!jobId || !applicantId) return;

        const res: RespParams<CandidateListResp> = await (
            await getResumeDetailApi({
                jobId,
                applicantId,
                jobType: location?.state?.isManualDistribution ? JOB_TYPE.jobPublish : JOB_TYPE.jobSpec,
            })
        ).json();
        if (res.code === 200) {
            const hardRequirements = res.data.application.portraitAnalysis.abilityLayerScore.hardRequirements
                .slice(0, 5)
                .map((item) => {
                    const currentTags = allTags.find((tag) => tag.id === item.tag);
                    return { ...currentTags, isMatched: item.isMatched };
                });
            const capabilityFit = res.data.application.portraitAnalysis.abilityLayerScore.capabilityFit
                .slice(0, 5)
                .map((item) => {
                    const currentTags = allTags.find((tag) => tag.id === item.tag);
                    return { ...currentTags, isMatched: item.isMatched };
                });
            const potentialForecast = res.data.application.portraitAnalysis.abilityLayerScore.potentialForecast
                .slice(0, 5)
                .map((item) => {
                    const currentTags = allTags.find((tag) => tag.id === item.tag);
                    return { ...currentTags, isMatched: item.isMatched };
                });

            res.data.allTags = [...hardRequirements, ...capabilityFit, ...potentialForecast].filter(
                (item) => item.isMatched
            );

            res.data.application.portraitAnalysis.abilityLayerScore.hardRequirementsTags = hardRequirements;
            res.data.application.portraitAnalysis.abilityLayerScore.capabilityFitTags = capabilityFit;
            res.data.application.portraitAnalysis.abilityLayerScore.potentialForecastTags = potentialForecast;
            setDetailInfo(res.data);
            setJobId(res.data.application.jobId);
            setApplicantId(res.data.application.applicantId);

            // 获取面试记录数据
            getInterviewTranscriptData(res.data.application.jobId, res.data.application.applicantId);
        }
    };
    // 获取简化后简历列表
    const getSimpleResumeList = async () => {
        const res: RespPaginationParams<SimpleCandidateListResp> = await (
            await getSimpleCandidateListApi({
                ...location?.state?.searchForm,
                pageNum: 0,
                pageSize: 999999,
            })
        ).json();

        if (res.code === 200) {
            setSimpleCandidateList(res.data.records ?? []);
        }
    };

    // 获取面试记录数据
    const getInterviewTranscriptData = async (jobId: string | null, applicantId: string | null) => {
        if (!jobId || !applicantId) return;

        setLoadingInterviewTranscript(true);
        try {
            const params: InterviewTranscriptReq = {
                jobId,
                applicantId,
            };

            const res = await getInterviewTranscriptApi(params);
            const result: RespParams<InterviewTranscriptResp[]> = await res.json();

            if (result.code === 200 && result.data) {
                setInterviewTranscriptData(result.data);
                setHasInterviewTranscript(result.data.length > 0);
            } else {
                setInterviewTranscriptData([]);
                setHasInterviewTranscript(false);
            }
        } catch (error) {
            console.error("获取面试记录失败:", error);
            setInterviewTranscriptData([]);
            setHasInterviewTranscript(false);
        } finally {
            setLoadingInterviewTranscript(false);
        }
    };

    const currentStage = useMemo(() => {
        return statusList.find((item) => item.stageId === detailInfo?.application?.trackStageId);
    }, [detailInfo, statusList]);
    const currentState = useMemo(() => {
        return statusList
            .find((item) => item.stageId === detailInfo?.application?.trackStageId)
            ?.trackStateStatistics.find((item) => item.stateId === detailInfo?.application?.trackStatusId);
    }, [detailInfo, statusList]);

    useEffect(() => {
        if (allTags.length > 0 && jobId && applicantId) {
            getResumeDetail(jobId, applicantId);
        }
    }, [allTags, jobId, applicantId]);

    useEffect(() => {
        setSimpleCandidateList([]);
        if (allTags.length === 0) {
            getAllTags();
        }
        setOriginType(location?.state?.mode ?? "talent");
        setIsManualDistribution(location?.state?.isManualDistribution);
        if (location?.state?.searchForm) {
            getSimpleResumeList();
        }
        setJobId(jobId ?? location?.state?.jobId);
        setApplicantId(applicantId ?? location?.state?.applicantId);
    }, []);

    useEffect(() => {
        if (!location?.state?.isManualDistribution && jobId) {
            getInterviewFlowList();
        }
    }, [jobId]);

    useEffect(() => {
        getFileInfoDetail();
    }, [detailInfo]);

    const tabsItem = useMemo(() => {
        let items = [
            {
                label: "原始简历",
                key: "1",
                children: (
                    <div>
                        {resumeBlob ? (
                            <RenderFile blob={resumeBlob} fileName={`${detailInfo?.profile?.name ?? ""}-原始简历`} />
                        ) : (
                            <Empty />
                        )}
                    </div>
                ),
            },
            // {
            //     label: "标准简历",
            //     key: "2",
            //     children: <StandardResume resumeData={null} />,
            // },
            // {
            //     label: "附件信息",
            //     key: "3",
            //     children: (
            //         <div className={styles["attachments-wrapper"]}>
            //             {/* {attachments.length > 0 ? (
            //             attachments.map((item) => (
            //                 <Flex key={item} justify="space-between" align="center">
            //                     <span>{item}</span>
            //                     <Button>预览</Button>
            //                     <Button>下载</Button>
            //                 </Flex>
            //             ))
            //         ) : (
            //             <Empty />
            //         )} */}
            //             <Empty />
            //             {/* <Button
            //             onClick={() => previewFileRef.current?.showDialog("3394f874-d0ac-4b2b-b6b0-2e1bf7f7f644.docx")}
            //         ></Button> */}
            //         </div>
            //     ),
            // },
            {
                label: "测评报告",
                key: "4",
                children: (
                    <EvaluationReport
                        key={detailInfo?.application?.applicantId ?? ""}
                        applicantId={detailInfo?.application?.applicantId ?? ""}
                        jobId={detailInfo?.application?.jobId ?? ""}
                        profileName={detailInfo?.profile?.name ?? ""}
                    />
                ),
            },
            {
                label: "操作记录",
                key: "5",
                children: <OperationLogs logs={detailInfo?.trackLogs ?? []} />,
            },
        ];
        // 根据是否有面试记录来决定是否添加面试记录tab
        if (hasInterviewTranscript) {
            items.push({
                label: "面试记录",
                key: "6",
                children: <InterviewTranscript data={interviewTranscriptData} loading={loadingInterviewTranscript} />,
            });
        }
        // 是否允许查看AI面试题
        if (["U0001", "U0002", "U0003", "U0012", "U0013"].includes(currentState?.stateCode ?? "")) {
            const exists = items.find((item) => item.key === "7");
            if (!exists) {
                items = [
                    ...items,
                    {
                        label: "AI面试题目",
                        key: "7",
                        children: (
                            <AIInterviewQuestion
                                key={detailInfo?.application?.applicantId}
                                applicantId={detailInfo?.application?.applicantId}
                                currentStage={currentStage}
                            />
                        ),
                    },
                ];
            }
        } else {
            const index = items.findIndex((item) => item.key === "7");
            if (index > -1) {
                items.splice(index, 1);
            }
        }

        return items;
    }, [
        currentStage,
        currentState,
        detailInfo,
        resumeBlob,
        hasInterviewTranscript,
        interviewTranscriptData,
        loadingInterviewTranscript,
    ]);

    // 是否为简历初筛阶段
    const isResumeFirstStage = useMemo(() => {
        return currentStage?.stageCode.includes("S0001") && currentState?.stateCode.includes("U0007");
    }, [currentStage]);

    // 是否为待定状态
    const isPendingStage = useMemo(() => {
        return (
            (currentState?.stateCode.includes("U0006") || currentState?.stateCode.includes("U0004")) &&
            originType === "HR"
        );
    }, [currentState]);

    // 是否为待面试状态
    const isInterviewStage = useMemo(() => {
        return currentState?.stateCode.includes("U0002") && originType === "Interviewer";
    }, [currentState]);

    // 是否允许进行面试结果填写
    const isShowInterviewResult = useMemo(() => {
        // 当HR查看时，只允许简历初筛阶段进行面试结果填写
        if (originType === "HR" && isResumeFirstStage) return true;
        // 当面试官查看时，默认允许进行面试结果填写
        if (originType === "Interviewer") {
            return isResumeFirstStage || currentState?.stateCode.includes("U0003");
        }

        return false;
    }, [originType, isResumeFirstStage, currentState]);

    // 是否允许加入职位
    const isAddJob = useMemo(() => {
        return isResumeFirstStage || currentState?.stateCode.includes("U0005");
    }, [isResumeFirstStage, currentState]);

    const stageName = useMemo(() => {
        if (!currentStage || !currentState) return "待分配";

        return `${currentStage?.stageName}(${currentState?.stateName})`;
    }, [currentStage, currentState]);

    const getFileInfoDetail = async () => {
        if (detailInfo?.profile?.applicantId) {
            try {
                const blob: Blob = await getFileApi({
                    jobId: detailInfo?.application.jobId,
                    applicationId: detailInfo?.profile?.applicantId,
                    isOrigin: true,
                    isManualDistribution: isManualDistribution,
                });
                setResumeBlob(blob);
            } catch (error) {
                setResumeBlob(null);
            }
        }
    };

    const handleInterviewResult = () => {
        interviewEvaluationRef.current?.showDrawer(
            isResumeFirstStage ? EVALUATE_TYPE.first : EVALUATE_TYPE.other,
            currentStage?.reviewTemplate ?? "",
            detailInfo?.application.applicantId ?? "",
            detailInfo?.application?.trackStageId ?? "",
            detailInfo?.application?.jobId ?? ""
        );
    };

    const handleNextStage = async () => {
        if (detailInfo?.application.applicantId && detailInfo?.application.jobId) {
            const res: RespParams<any> = await (
                await candidateNextStageApi({
                    applicantId: detailInfo?.application.applicantId,
                    jobId: detailInfo?.application.jobId,
                })
            ).json();

            if (res.code === 200) {
                messageService.success("操作成功");
                getResumeDetail(jobId, applicantId);
            }
        }
    };

    const handleChangeStatus = () => {
        if (applicantId && jobId && currentStage) {
            changeStatusRef.current?.showDrawer(applicantId, jobId, stageName, currentStage);
        }
    };

    const handleStartInterview = async () => {
        const result: InterviewTimeResp | null = await new Promise((resolve) => {
            Modal.confirm({
                title: "是否开始面试?",
                icon: <ExclamationCircleFilled />,
                content: "点击确定后开始面试",
                onOk() {
                    return new Promise((resolve2, reject2) => {
                        startInterviewApi({
                            applicantId: detailInfo?.application.applicantId ?? "",
                            stageId: detailInfo?.application?.trackStageId ?? "",
                        })
                            .then((response) => response.json())
                            .then((res: any) => {
                                if (res.code === 200) {
                                    messageService.success(res?.msg ?? "操作成功");
                                    resolve2(true);
                                    resolve(res.data);
                                } else {
                                    reject2(false);
                                }
                            })
                            .catch((err) => {
                                reject2(false);
                            });
                    });
                },
                onCancel() {
                    resolve(null);
                },
            });
        });

        if (result) {
            setInterviewInfo({
                id: result?.id,
                summary: result.summary,
                meetingLink: result?.meetingLink,
                description: result.description.replace(/飞书会议链接：.*(\n|$)/g, ""),
            });
            setShowInterviewInfo(true);
        }
    };

    const handleChangeCandidateDetail = (step: number) => {
        const currentIdx = simpleCandidateList.findIndex(
            (item) => item.applicantId === detailInfo?.application.applicantId
        );
        if (currentIdx === -1) {
            messageService.error("无法获取简历，切换失败！");
            return;
        }
        if (currentIdx + step < 0) {
            messageService.warning("当前为第一份简历！");
            return;
        }
        if (currentIdx + step >= simpleCandidateList.length) {
            messageService.warning("当前为最后一份简历！");
            return;
        }

        const current = simpleCandidateList?.[currentIdx + step];
        if (current?.jobId && current?.applicantId) {
            setJobId(current?.jobId);
            setApplicantId(current?.applicantId);
        }
    };

    const handleAddJob = () => {
        addJobRef.current?.showDrawer(
            detailInfo?.application.applicantId ?? "",
            stageName,
            location.state?.jobName ?? ""
        );
    };

    return (
        <Layout id="resumeDetailId" className={styles["talent-detail-wrapper"]}>
            <Header title="返回列表" containerId="resumeDetailId" />
            <Layout.Content>
                <div className={styles["talent-detail-content"]}>
                    <div className={styles["talent-detail-content__left__wrapper"]}>
                        <div className={styles["talent-detail-content__left"]}>
                            <div className={styles["talent-detail-content__basic-info"]}>
                                <div className={styles["talent-detail-content__basic-info__user"]}>
                                    <div className={styles["talent-detail-content__basic-info__user__left"]}>
                                        <Avatar size={48} src={null} alt="用户头像" />
                                        <div className={styles["user-info"]}>
                                            <div>
                                                <span className={styles["name"]}>{detailInfo?.profile.name}</span>
                                                <Divider
                                                    type="vertical"
                                                    style={{ width: "2px", backgroundColor: "#00000042" }}
                                                />
                                                <span>
                                                    {detailInfo?.profile.gender === "男"
                                                        ? "男"
                                                        : detailInfo?.profile.gender === "女"
                                                          ? "女"
                                                          : "暂无"}
                                                </span>
                                                <Badge color="#0000006B"></Badge>
                                                <span>
                                                    {detailInfo?.profile.birth
                                                        ? `${calculateAge(detailInfo.profile.birth)}岁`
                                                        : "暂无"}
                                                </span>
                                                <Badge color="#0000006B"></Badge>
                                                <span>{detailInfo?.profile.workExperienceYears}年工作经验</span>
                                                <Badge color="#0000006B"></Badge>
                                                <span>暂无</span>
                                            </div>
                                            <Flex>
                                                <Flex align="center" gap={8}>
                                                    <WorkExperienceIcon />
                                                    <span>{detailInfo?.profile.phone}</span>
                                                    <CopyIcon
                                                        className={styles["copy-icon"]}
                                                        style={{ marginRight: 20 }}
                                                        onClick={(e: any) => {
                                                            e.stopPropagation();
                                                            copyToClipboard(detailInfo?.profile?.phone ?? "");
                                                        }}
                                                    />
                                                </Flex>
                                                <Flex align="center" gap={8}>
                                                    <WorkExperienceIcon />
                                                    <span>{detailInfo?.profile.email}</span>
                                                    <CopyIcon
                                                        className={styles["copy-icon"]}
                                                        onClick={(e: any) => {
                                                            e.stopPropagation();
                                                            copyToClipboard(detailInfo?.profile?.email ?? "");
                                                        }}
                                                    />
                                                </Flex>
                                            </Flex>
                                        </div>
                                    </div>
                                    {detailInfo?.application.portraitAnalysis && (
                                        <Popover
                                            trigger="click"
                                            open={showPopover}
                                            onOpenChange={(open: boolean) => {
                                                setShowPopover(open);
                                            }}
                                            content={
                                                <ProfileFit
                                                    detail={detailInfo?.application.portraitAnalysis}
                                                    aiAnalysisStr={
                                                        detailInfo?.application.portraitMatching.aiEvaluation
                                                    }
                                                />
                                            }
                                            title={() => (
                                                <Flex justify="space-between" align="center">
                                                    <span>画像匹配度</span>
                                                    <CloseOutlined onClick={() => setShowPopover(false)} />
                                                </Flex>
                                            )}
                                        >
                                            <div
                                                className={styles["talent-detail-content__basic-info__user__right"]}
                                                onClick={() => setShowPopover(true)}
                                            >
                                                <span className={styles["label"]}>画像匹配</span>
                                                <Rate
                                                    disabled
                                                    allowHalf
                                                    value={detailInfo?.application.portraitAnalysis.matchScore}
                                                />
                                                {detailInfo?.application.portraitAnalysis.matchScore}
                                            </div>
                                        </Popover>
                                    )}
                                </div>
                                <div className={styles["talent-detail-content__basic-info__tags"]}>
                                    <Tags
                                        tags={
                                            detailInfo?.allTags?.map((item) => ({
                                                name: item.name,
                                                isMatched: item.isMatched ?? true,
                                            })) ?? []
                                        }
                                        color="#465ce8"
                                    />
                                </div>
                                <Row gutter={16} style={{ margin: "16px 0" }}>
                                    <Col span={24}>
                                        <div className={styles["info-column"]}>
                                            <InfoFieldGroup firstIcon={<WorkExperienceIcon />}>
                                                {detailInfo?.profile.workExperience.map((item, index) => {
                                                    return (
                                                        <InfoField
                                                            key={index}
                                                            label={`${formatTime(item.startDate)}-${item.endDate ? formatTime(item.endDate) : "至今"}`}
                                                            labelWidth={170}
                                                            value={
                                                                <div>
                                                                    <span>{item.company}</span>
                                                                    <Divider
                                                                        type="vertical"
                                                                        style={{
                                                                            width: "2px",
                                                                            backgroundColor: "#00000042",
                                                                        }}
                                                                    />
                                                                    <span>{item.position}</span>
                                                                </div>
                                                            }
                                                        />
                                                    );
                                                })}
                                            </InfoFieldGroup>
                                            <InfoFieldGroup firstIcon={<EducationExperienceIcon />}>
                                                {detailInfo?.profile.educationExperience.map((item, index) => {
                                                    return (
                                                        <InfoField
                                                            key={index}
                                                            label={`${formatTime(item.startDate)}-${formatTime(item.endDate)}`}
                                                            labelWidth={170}
                                                            value={
                                                                <div>
                                                                    <span>{item.school}</span>
                                                                    <Divider
                                                                        type="vertical"
                                                                        style={{
                                                                            width: "2px",
                                                                            backgroundColor: "#00000042",
                                                                        }}
                                                                    />
                                                                    <span>{item.degree}</span>
                                                                </div>
                                                            }
                                                        />
                                                    );
                                                })}
                                            </InfoFieldGroup>
                                        </div>
                                    </Col>
                                </Row>
                                <div className={styles["talent-detail-content__basic-info__experience"]}></div>
                                {detailInfo?.application.portraitMatching.simplifiedReview && (
                                    <div className={styles["talent-detail-content__basic-info__AIReason"]}>
                                        <AiSuggestionPrefix className={styles["prefix-icon"]} />
                                        <span className={styles["ai-content"]}>
                                            {detailInfo?.application.portraitMatching.simplifiedReview}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <div className={styles["talent-detail-content__files"]}>
                                <Tabs defaultActiveKey={"1"} items={tabsItem} />
                            </div>
                        </div>
                        {simpleCandidateList.length > 1 && (
                            <div
                                className={`${styles["pagination-btn"]} ${styles["left"]}`}
                                onClick={() => handleChangeCandidateDetail(-1)}
                            >
                                <ArrowLeftOutlined />
                            </div>
                        )}
                        {simpleCandidateList.length > 1 && (
                            <div
                                className={`${styles["pagination-btn"]} ${styles["right"]}`}
                                onClick={() => handleChangeCandidateDetail(1)}
                            >
                                <ArrowRightOutlined />
                            </div>
                        )}
                    </div>
                    <div className={styles["talent-detail-content__right"]}>
                        <div className={styles["talent-detail-content__right__top"]}>
                            <Flex gap={8} align="center">
                                <span className={styles["name"]}>{location.state?.jobName ?? ""}</span>
                                <Tag style={{ color: "#0099F2FF", backgroundColor: "#0099F226" }} bordered={false}>
                                    {stageName}
                                </Tag>
                            </Flex>
                            <div>
                                <span>暂无</span>
                                <Badge color="#0000006B"></Badge>
                                <span>暂无</span>
                                <Badge color="#0000006B"></Badge>
                                <span>暂无</span>
                                <Badge color="#0000006B"></Badge>
                                <span>{detailInfo?.profile.createdAt}&nbsp;&nbsp;&nbsp;投递</span>
                            </div>
                            <Flex gap={8} wrap>
                                {isShowInterviewResult && (
                                    <Button size="small" type="primary" onClick={handleInterviewResult}>
                                        填写评价
                                    </Button>
                                )}
                                {isPendingStage && (
                                    <Button size="small" type="primary" onClick={handleNextStage}>
                                        进入下一阶段
                                    </Button>
                                )}
                                {isInterviewStage && (
                                    <Button size="small" type="primary" onClick={handleStartInterview}>
                                        开始面试
                                    </Button>
                                )}
                                <Button size="small" onClick={handleChangeStatus}>
                                    转移状态
                                </Button>
                                {/* {isAddJob && (
                                    <Button size="small" onClick={handleAddJob}>
                                        加入职位
                                    </Button>
                                )} */}
                                {/* <Button size="small">加入文件夹</Button>
                                <Button size="small">填写备注</Button> */}
                            </Flex>
                        </div>
                        <div className={styles["talent-detail-content__right__bottom"]}>
                            {/* <div className={styles["name"]}>其他投递({detailInfo?.otherJobList?.length ?? 0})</div> */}
                            <div className={styles["name"]}>其他投递(0)</div>
                            <Flex
                                justify="center"
                                align="center"
                                style={{ height: "calc(100% - 18px)", width: "100%" }}
                            >
                                <Empty />
                            </Flex>
                            {/* {detailInfo?.otherJobList?.length > 0 ? (
                                <div
                                    key={"otherJobList"}
                                    className={styles["talent-detail-content__right__bottom__collapse"]}
                                >
                                    {detailInfo?.otherJobList?.map((item: any) => {
                                        return <JobCollapse key={item.key} data={item} />;
                                    })}
                                </div>
                            ) : (
                                <Flex
                                    justify="center"
                                    align="center"
                                    style={{ height: "calc(100% - 18px)", width: "100%" }}
                                >
                                    <Empty />
                                </Flex>
                            )} */}
                        </div>
                    </div>
                </div>
            </Layout.Content>
            <PreviewFile ref={previewFileRef} />
            <InterviewEvaluation ref={interviewEvaluationRef} refresh={() => getResumeDetail(jobId, applicantId)} />
            <ChangeStatus ref={changeStatusRef} mode={originType} refresh={() => getResumeDetail(jobId, applicantId)} />
            <AddJob ref={addJobRef} refresh={() => getResumeDetail(jobId, applicantId)} />
            <ChatDialog
                open={showInterviewInfo}
                title="面试信息"
                onCancel={() => {
                    setShowInterviewInfo(false);
                    getResumeDetail(jobId, applicantId);
                }}
                footer={null}
                confirmLoading={false}
            >
                <div className={styles["interview-info-wrapper"]}>
                    <div className={styles["interview-info-title"]}>{interviewInfo?.summary}</div>
                    <Flex>
                        <div style={{ width: 170 }}>会议链接：</div>
                        <Link href={interviewInfo?.meetingLink} target="_blank">
                            {interviewInfo?.meetingLink}
                        </Link>
                    </Flex>
                    <div style={{ whiteSpace: "pre-wrap" }}>{interviewInfo?.description}</div>
                </div>
            </ChatDialog>
        </Layout>
    );
};

export default TalentDetail;
