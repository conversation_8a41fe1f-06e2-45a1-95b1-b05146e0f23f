import { forwardRef, useImperative<PERSON>andle, useRef, useState, useEffect, useCallback, useMemo } from "react";
import ChatDialog from "../../components/ChatDialog";
import { Col, Form, Input, Radio, Row, Segmented, Select, DatePicker, Avatar, Flex, Empty } from "antd";
import { PlusOutlined, InfoCircleOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { getAllUserListApi } from "../../request/modules/common";
import InterviewerSelect from "../../components/InterviewerSelect";
import { RespUser } from "../../components/typing";
import messageService from "../../lib/message";
import {
    CandidateListResp,
    InitInterviewReq,
    InterviewerListReq,
    InterviewerScheduleResp,
    InterviewEvaluationTemplateResp,
    ScheduleInterviewExpose,
    TrackStageStatistic,
} from "../../store/modules/candidate";
import {
    addInterviewScheduleApi,
    getInterviewEvaluationTemplateApi,
    getInterviewScheduleListApi,
} from "../../request/modules/candidate";
import { RespParams } from "../../types/typing";
import { SCHEDULE_TYPE } from "../../constant";
import { formatTime } from "../../utils/utils";
import dayjs from "dayjs";
import useFullScreenStore from "../../store/modules/fullscreen";
import { debounce } from "lodash-es";
import { TextView } from "../../components/TextView";

const ScheduleInterview = forwardRef<ScheduleInterviewExpose, { refresh: () => void }>((props, ref) => {
    const { refresh } = props;
    const { RangePicker } = DatePicker;

    const [showDialog, setShowDialog] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    // 手动安排面试官信息
    // const [interviewerList, setInterviewerList] = useState<InterviewerListReq[]>([]);
    // 自动安排面试官信息
    const [autoInterviewerList, setAutoInterviewerList] = useState<InterviewerListReq[]>([]);
    const [resumeList, setResumeList] = useState<CandidateListResp[]>([]);
    const [showUserSelect, setShowUserSelect] = useState(false);
    const [selectedUserData, setSelectedUserData] = useState<RespUser[]>([]);
    const [currentStage, setCurrentStage] = useState<TrackStageStatistic>();
    const [currentStartTime, setCurrentStartTime] = useState<dayjs.Dayjs>();
    // 搜索值
    const [empName, setEmpName] = useState<string>("");
    // 面试官选项
    const [interviewerOptions, setInterviewerOptions] = useState<any>([]);

    // const { fullScreenContainerId } = useFullScreenStore((state) => ({
    //     fullScreenContainerId: state.fullScreenContainerId,
    // }));

    const [form] = Form.useForm();
    const scheduleType = Form.useWatch("scheduleType", form);
    const interviewType = Form.useWatch("interviewType", form);

    const userSelectRef = useRef<any>(null);

    useImperativeHandle(ref, () => {
        return {
            showDialog: async (currentStage: TrackStageStatistic, resumeList: CandidateListResp[]) => {
                setSelectedUserData([]);
                form.resetFields();

                setShowDialog(true);
                // setInterviewerList([]);
                setInterviewerOptions([]);
                setResumeList(resumeList);
                setCurrentStage(currentStage);
                const today = new Date();
                const threeDaysAfter = new Date(today);
                threeDaysAfter.setDate(today.getDate() + 3);
                const startTime = formatTime(today, "YYYY-MM-DD 08:30:00");
                const endTime = formatTime(threeDaysAfter, "YYYY-MM-DD 20:30:00");
                if (currentStage?.handlers && currentStage.handlers.length > 0) {
                    const res: RespParams<InterviewerScheduleResp> = await (
                        await getInterviewScheduleListApi({
                            targetEmpId: currentStage?.handlers,
                            startTime,
                            endTime,
                            period: SCHEDULE_TYPE.free,
                        })
                    ).json();

                    if (res.code === 200) {
                        const entries = Object.entries(res.data);

                        if (entries.length > 0) {
                            const interviewerList = entries.map(([key, value]) => {
                                let time = "";
                                let timeList = value.items.map((item) => item.gmt_end.split(" ")[0]);
                                timeList = [...new Set(timeList)].sort((prev, cur) => {
                                    return new Date(prev).getTime() - new Date(cur).getTime();
                                });
                                time = timeList.join(",");

                                return {
                                    id: key,
                                    name: `${value.info.emp_id}-${value.info.name}`,
                                    avatar: value.info.avatar,
                                    time,
                                };
                            });
                            // console.log("abcd", interviewerList);
                            if (interviewerList.length > 0) {
                                // setInterviewerList(interviewerList);
                                setAutoInterviewerList(interviewerList);
                            }
                        } else {
                            setAutoInterviewerList([]);
                        }
                    }
                }
                if (currentStage?.reviewTemplate) {
                    const res: RespParams<InterviewEvaluationTemplateResp> = await (
                        await getInterviewEvaluationTemplateApi(currentStage?.reviewTemplate)
                    ).json();

                    if (res.code === 200) {
                        form.setFieldValue("templateId", res.data.title);
                    }
                }
            },
        };
    });

    const getAllUserListApiFC = useCallback(
        async (searchValue?: string) => {
            try {
                const res: RespParams<{ records: RespUser[] }> = await (
                    await getAllUserListApi({
                        pageNum: 0,
                        pageSize: 50, // Increased page size for better search results
                        empName: searchValue || empName,
                        deptCodePath: "100000",
                    })
                ).json();

                if (res.code === 200 && res.data?.records) {
                    // Transform user data to match interviewer list format
                    const userOptions = res.data.records.map((user: RespUser) => ({
                        id: user.empId,
                        name: `${user.empId}-${user.empName}`,
                    }));
                    setInterviewerOptions(userOptions);
                }
            } catch (error) {
                console.error("Failed to fetch user list:", error);
            }
        },
        [empName]
    );

    const debouncedSearch = useMemo(
        () =>
            debounce((searchValue: string) => {
                getAllUserListApiFC(searchValue);
            }, 400),
        [getAllUserListApiFC]
    );

    useEffect(() => {
        getAllUserListApiFC();
        // Cleanup debounced function on unmount
        return () => {
            debouncedSearch.cancel();
        };
    }, [getAllUserListApiFC, debouncedSearch]);

    useEffect(() => {
        if (showUserSelect && userSelectRef.current) {
            const timer = setTimeout(() => {
                const interviewers = selectedUserData.map((user) => ({
                    emp_id: user.empId,
                    name: user.empName,
                    avatar: user.avatar,
                    opr_id: "",
                    spare_date: [],
                }));
                userSelectRef.current?.setSelectedUser(interviewers);
                clearTimeout(timer);
            }, 300);
            return () => clearTimeout(timer);
        }
    }, [showUserSelect, selectedUserData]);

    const handleInterviewerSearch = (searchValue: string) => {
        setEmpName(searchValue);
        debouncedSearch(searchValue);
    };

    const handleSelectUser = () => {
        const selectedInterviewers = userSelectRef.current?.getSelectedUser;

        if (selectedInterviewers && selectedInterviewers.length > 0) {
            // 将 QueryAvailableInterviewerDataResponse 转换为 RespUser 格式
            const users = selectedInterviewers.map((interviewer: any) => ({
                empId: interviewer.emp_id,
                empName: interviewer.name,
                avatar: interviewer.avatar,
                spare_date: interviewer.spare_date,
            }));
            setSelectedUserData(users);
            setShowUserSelect(false);
        } else {
            setSelectedUserData([]);
            setShowUserSelect(false);
        }
    };

    const onOk = async () => {
        const validateRes = await form.validateFields();
        const formValue = form.getFieldsValue();

        if (validateRes) {
            setConfirmLoading(true);
            // const params: InitInterviewReq = {
            const params: InitInterviewReq = {
                evalTempId: currentStage?.reviewTemplate ?? "",
                candidates: resumeList.map((item) => {
                    return {
                        resumeId: item.profile.applicantId,
                        stageId: currentStage?.stageId ?? "",
                    };
                }),
                interviewers: [formValue.interviewer],
                isVideo: formValue.scheduleType === 2 || formValue.interviewType === 2,
                emailTemplate: "",
                positionName: "test",
            };
            // 线下面试、手动安排传递
            if (formValue.scheduleType === 1) {
                const timeScopes: { [key: string]: { start: string; end: string } } = {};
                timeScopes[formValue.interviewer] = {
                    start: formValue.interviewTime?.[0]?.format("YYYY-MM-DD HH:mm:ss"),
                    end: formValue.interviewTime?.[1]?.format("YYYY-MM-DD HH:mm:ss"),
                };
                params.manuallyAssignSettings = {
                    loc: formValue.interviewAddress,
                    timeScopes,
                };
            }
            if (formValue.scheduleType === 2) {
                // 获取选中的面试官
                const selectedInterviewers = userSelectRef.current?.getSelectedUser;
                if (selectedInterviewers && selectedInterviewers.length > 0) {
                    params.interviewers = selectedInterviewers.map((item: any) => item.emp_id);
                } else {
                    // 如果没有选择，使用自动安排的面试官
                    params.interviewers = autoInterviewerList.map((item) => item.id);
                }

                params.manuallyAssignSettings = undefined;
            }

            try {
                const res: RespParams<any> = await (await addInterviewScheduleApi(params)).json();
                if (res.code === 200) {
                    messageService.success(res.msg ?? "安排成功");
                    setShowDialog(false);
                    setConfirmLoading(false);
                    refresh();
                }
            } catch (err) {
                setConfirmLoading(false);
            }
        }
    };

    return (
        <ChatDialog
            title="安排面试"
            width="45%"
            open={showDialog}
            confirmLoading={confirmLoading}
            onCancel={() => setShowDialog(false)}
            onOk={onOk}
        >
            <div className={styles["schedule-interview-wrapper"]}>
                <Form layout="vertical" form={form} initialValues={{ scheduleType: 1, interviewType: 1 }}>
                    <Form.Item name="scheduleType" style={{ marginBottom: 20 }}>
                        <Segmented
                            options={[
                                {
                                    label: "手动安排",
                                    value: 1,
                                },
                                {
                                    label: "自动安排",
                                    value: 2,
                                },
                            ]}
                        />
                    </Form.Item>
                    {scheduleType === 1 && (
                        <>
                            <Form.Item name="interviewType" label="面试方式" rules={[{ required: true }]}>
                                <Radio.Group
                                    options={[
                                        { value: 1, label: "线下面试" },
                                        { value: 2, label: "视频面试" },
                                    ]}
                                />
                            </Form.Item>
                            <div className="two-column-form">
                                <Form.Item name="interviewer" label="面试官" rules={[{ required: true }]}>
                                    <Select
                                        placeholder="请选择面试官"
                                        showSearch
                                        filterOption={false}
                                        onSearch={handleInterviewerSearch}
                                        options={interviewerOptions}
                                        fieldNames={{
                                            label: "name",
                                            value: "id",
                                        }}
                                        optionRender={(option) => (
                                            <Flex align="center" gap={12} key={option.data.id}>
                                                {/* <Avatar size={36} src={option.data.avatar} /> */}
                                                <Flex vertical>
                                                    <span style={{ fontSize: 14, color: "var(--main-text-color)" }}>
                                                        {option.data.name}
                                                    </span>
                                                    <span style={{ fontSize: 12, color: "var(--sub-text-color)" }}>
                                                        {option.data.time}
                                                    </span>
                                                </Flex>
                                            </Flex>
                                        )}
                                    />
                                </Form.Item>
                                {interviewType === 1 && (
                                    <Form.Item
                                        name="interviewAddress"
                                        label="面试地点"
                                        rules={[{ required: interviewType === 1 }]}
                                    >
                                        <Input placeholder="请输入面试地点" maxLength={200} allowClear />
                                    </Form.Item>
                                )}

                                <Form.Item
                                    name="interviewTime"
                                    label="面试时间"
                                    rules={[{ required: interviewType === 1 }]}
                                >
                                    <RangePicker
                                        showTime={{ hideDisabledOptions: true }}
                                        format="YYYY-MM-DD HH:mm"
                                        onCalendarChange={(value) => {
                                            setCurrentStartTime(value?.[0] ?? undefined);
                                        }}
                                        disabledDate={(current) => {
                                            if (
                                                currentStartTime &&
                                                (current.isAfter(currentStartTime.clone().endOf("day")) ||
                                                    current.isBefore(currentStartTime.clone().startOf("day")))
                                            )
                                                return true;

                                            if (!current) return false;
                                            if (current.isBefore(dayjs().add(1, "day").startOf("day"))) return true;

                                            return false;
                                        }}
                                        disabledTime={() => {
                                            return {
                                                disabledHours: () => {
                                                    // 禁用0-7点和21-23点
                                                    return [
                                                        ...Array.from({ length: 8 }, (_, i) => i), // 0-7点
                                                        ...Array.from({ length: 3 }, (_, i) => i + 21),
                                                    ]; // 21-23点
                                                },
                                                disabledMinutes: (selectedHour) => {
                                                    if (selectedHour === 8) {
                                                        // 如果选择8点，禁用0-29分
                                                        return Array.from({ length: 30 }, (_, i) => i);
                                                    }
                                                    if (selectedHour === 20) {
                                                        // 如果选择20点，禁用31-59分
                                                        return Array.from({ length: 29 }, (_, i) => i + 31);
                                                    }
                                                    return [];
                                                },
                                            };
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item name="templateId" label="面评表">
                                    <Input disabled placeholder="请输入面评表" maxLength={200} allowClear />
                                </Form.Item>
                            </div>
                        </>
                    )}
                    {scheduleType === 2 && (
                        <>
                            <div className={styles["main-title"]}>视频面试</div>
                            <div>面试官</div>
                            {selectedUserData.length > 0 && (
                                <div className={styles["interviewer-list"]}>
                                    {selectedUserData.map((item) => {
                                        return (
                                            <div key={item.empId} className={styles["interviewer-item"]}>
                                                <Avatar size={36} src={item.avatar} style={{ flexShrink: 0 }} />
                                                <Flex vertical>
                                                    <span className={styles["name"]}>
                                                        {item.empId}-{item.empName}
                                                    </span>
                                                    <span className={styles["time"]}>{item.spare_date?.join(",")}</span>
                                                </Flex>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                            <span
                                className={styles["select-interviewer-button"]}
                                onClick={() => {
                                    setShowUserSelect(true);

                                    const timer = setTimeout(() => {
                                        userSelectRef.current?.setSelectedUser(selectedUserData);
                                        clearTimeout(timer);
                                    }, 300);
                                }}
                            >
                                <PlusOutlined />
                                选择面试官
                            </span>
                            <div className={styles["hint-message"]}>
                                <InfoCircleOutlined />
                                <span>不选择面试官，将按照系统配置自动安排</span>
                            </div>
                            {autoInterviewerList.length > 0 ? (
                                <div className={styles["interviewer-list"]}>
                                    {autoInterviewerList.map((item) => {
                                        return (
                                            <div key={item.id} className={styles["interviewer-item"]}>
                                                <Avatar size={36} src={item.avatar} style={{ flexShrink: 0 }} />
                                                <Flex vertical>
                                                    <span className={styles["name"]}>{item.name}</span>
                                                    <span className={styles["time"]}>{item.time}</span>
                                                </Flex>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无空闲日程面试官信息" />
                            )}
                            <div className={styles["interview-review"]}>
                                <Form.Item name="templateId" label="面评表">
                                    <Input disabled placeholder="请输入面评表" maxLength={200} allowClear />
                                </Form.Item>
                            </div>
                        </>
                    )}
                </Form>
            </div>
            <ChatDialog
                open={showUserSelect}
                title="选择面试官"
                width="75%"
                onOk={handleSelectUser}
                onCancel={() => setShowUserSelect(false)}
                confirmLoading={false}
            >
                {/* 老版本的选择面试官处理，样式为左侧是部门树，右侧是面试官列表 */}
                {/* <UserSelect mode="checkbox" ref={userSelectRef} /> */}
                {/* 新版本是左侧为当前部门有空闲日程的面试官树，右侧是卡片式的面收官卡片 */}
                <InterviewerSelect ref={userSelectRef} />
            </ChatDialog>
        </ChatDialog>
    );
});

ScheduleInterview.displayName = "ScheduleInterview";
export default ScheduleInterview;
