"use client";

import { <PERSON><PERSON>, <PERSON> } from "antd";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import useUserInfoStore, { UserInfoResp } from "@/app/store/userInfo";
import styles from "./index.module.scss";
import { RespParams } from "@/app/typing";
import messageService from "@/app/lib/message";
import { Path } from "@/app/constant";

export default function Auth() {
    const [error, setError] = useState(null);
    const searchParams = useSearchParams();

    const { setToken, setLogging, setUser, setState } = useUserInfoStore((state) => ({
        setToken: state.setToken,
        setLogging: state.setLogging,
        setUser: state.setUser,
        setState: state.setState,
    }));

    // 重定向到 CAS 登录页面
    const redirectToCAS = () => {
        const url = `${process.env.NEXT_PUBLIC_CAS_URL}cas/authorize?clientId=${process.env.NEXT_PUBLIC_CAS_CLIENT_ID}&service=${encodeURIComponent(
            `${process.env.NEXT_PUBLIC_CAS_SERVICE_URL}auth/sso/callback`
        )}`;
        window.location.href = url;
    };

    // 处理认证回调
    const handleCallback = async () => {
        try {
            const ticket = searchParams.get("ticket");
            const code = searchParams.get("code");

            if (!ticket && !code) {
                throw new Error("未收到票据");
            }

            let res: RespParams<UserInfoResp> | null = null;

            if (ticket) {
                // 获取从cas系统登录的用户信息
                res = await (
                    await fetch(`${process.env.NEXT_PUBLIC_FETCH_BASE_API}auth/sso/access-token?ticket=${ticket}`)
                ).json();
            } else if (code) {
                // 获取从飞书系统登录的用户信息
                res = await (
                    await fetch(`${process.env.NEXT_PUBLIC_FETCH_BASE_API}auth/oauth/feishu/access-token?code=${code}`)
                ).json();
            }

            if (res && res.code === 200) {
                if (res?.data.token) {
                    setToken("Bearer " + res.data.token);
                }
                setUser(res.data.user);
                setLogging(false);
                // 使用router或者navigate会出现报错，使用原生window.location.href可以正常跳转
                window.location.href = "/#" + Path.Dashboard;
            }
        } catch (err: any) {
            console.error("认证回调处理失败:", err);
            setError(err?.message ?? "登录失败，请重试");
            messageService.error(err?.message ?? "登录失败，请重试");
            setLogging(false);
        }
    };

    useEffect(() => {
        // 检查是否有认证参数，如果有则处理回调，否则重定向到登录页
        const ticket = searchParams.get("ticket");
        const code = searchParams.get("code");

        if (ticket) {
            // 处理回调
            setState("cas");
            handleCallback();
        } else if (code) {
            setState("feishu");
            handleCallback();
        } else {
            // 重定向到 CAS 登录页面
            redirectToCAS();
        }
    }, [searchParams]);

    return (
        <div className={styles["auth-client-wrapper"]}>
            <div className={styles["loading-container"]}>
                {error && (
                    <div className={styles["error-message"]}>
                        {error}
                        <Button onClick={redirectToCAS} style={{ marginTop: "10px" }}>
                            重试登录
                        </Button>
                    </div>
                )}
                {!error && (
                    <Spin tip="登录中，请稍后。。。" size="large">
                        {
                            <div
                                style={{
                                    padding: 50,
                                    borderRadius: 4,
                                }}
                            />
                        }
                    </Spin>
                )}
            </div>
        </div>
    );
}
