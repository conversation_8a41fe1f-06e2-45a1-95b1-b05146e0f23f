import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useMemo, useRef, useState } from "react";
import markdownIt from "markdown-it";
import { Button, Cascader, Form, Input, InputNumber, Select } from "antd";
import TextArea from "antd/es/input/TextArea";
import RichTextEditor, { RichTextEditorRef } from "../../../components/RichTextEditor";
import JobTagsPage from "./JobTags";
import styles from "./index.module.scss";
import { getAIMessageApi, getAITagsApi } from "../../../request/modules/dashboard";
import { RespParams, StreamMessage } from "../../../types/typing";
import {
    hiringBatchData,
    jobLocationData,
    jobTypeData,
    languageList,
    JobTags,
    assignmentList,
    ASSIGNMENT_TYPE_MANUAL,
} from "../constant";
import useJobFlowStore, {
    AddFlowItemExposeHandle,
    FlowItemParams,
    Job,
    TagsResp,
} from "../../../store/modules/dashboard/jobFlow";
import { MDXMarkdown } from "../../Markdown/MDX-markdown";
import { cloneDeep } from "lodash-es";
import { formatHtmlWithLists, getNodeWithAncestors } from "../../../utils/utils";
import { getJobListApi } from "../../../request/modules/common";
import { nanoid } from "nanoid";

const FlowItem = forwardRef<
    AddFlowItemExposeHandle,
    {
        item: FlowItemParams;
        handleJobNameChange: (val: string) => void;
        handleOrgChange: (val: string) => void;
        handleChangeLoading: (val: boolean) => void;
        mode?: string;
        allocationMethod?: number;
        customChildren?: React.ReactNode;
        handleAssignmentChange?: (val: number) => void;
    }
>((props, ref) => {
    const {
        item,
        handleJobNameChange,
        handleOrgChange,
        handleChangeLoading,
        mode,
        allocationMethod,
        customChildren,
        handleAssignmentChange,
    } = props;

    const { flowItems, departmentList, educationList, setFlowItems } = useJobFlowStore((state) => ({
        flowItems: state.flowItems,
        departmentList: state.departmentList,
        educationList: state.educationList,
        setFlowItems: state.setFlowItems,
    }));
    const [AIButtonLoading, setAIButtonLoading] = useState(false);

    useImperativeHandle(ref, () => ({
        getForm: () => form,
        richTextRef: richTextRef,
    }));

    const richTextRef = useRef<RichTextEditorRef>(null);

    const [form] = Form.useForm();
    const [currentJobList, setCurrentJobList] = useState<Job[]>([]);
    const [jobJDByAI, setJobJDByAI] = useState("");

    useEffect(() => {
        let str = "";
        if (item.jobJD) {
            str = item.jobJD.replaceAll("\n", "<br/>");
        } else {
            str = `<p><strong>业务介绍:</strong></p>
            <br/>
            <p><strong>工作职责:</strong></p>
            <br/>
            <p><strong>职位要求:</strong></p>
            <br/>
            `;
        }

        richTextRef.current?.setHTML(str);
    }, []);

    useEffect(() => {
        form.setFieldsValue(item);
        if (allocationMethod) {
            form.setFieldValue("assignment", allocationMethod);
        }
    }, [item, allocationMethod]);

    useEffect(() => {
        if (item.jobList && item.jobList.length > 0) {
            setCurrentJobList(item.jobList);
        }
    }, [item.jobList]);

    const getJobList = async (code: string) => {
        const res: RespParams<Job[]> = await (await getJobListApi({ orgNo: code })).json();
        if (res.code === 200) {
            item.jobList = res.data;
            const list = flowItems.map((item2) => {
                if (item2.jobId === item.jobId) {
                    item2.jobList = res.data;
                }
                return item2;
            });
            setFlowItems(list);
        }
    };

    const handleChangeJobNameInner = (value: string[]) => {
        const jobNameList = getNodeWithAncestors(item.jobList as any[], value[value.length - 1], "code");
        const jobNameInner = jobNameList.map((item) => item.desc).join("/");

        form.setFieldValue("jobNameInner", jobNameInner);
        const params = flowItems.find((item2) => item2.jobId === item.jobId);
        if (params) {
            params.jobNameInner = form.getFieldValue("jobNameInner");
        }

        handleJobNameChange(form.getFieldValue("jobNameInner"));
    };

    const handleChangeOrg = (value: string[], options: any[]) => {
        console.log("aaabbb", options);
        if (options.length > 0) {
            if (options[options.length - 1].dept_level === 90) {
                return;
            }
        }
        const orgNameList = getNodeWithAncestors(departmentList, value[value.length - 1], "dept_code");
        const orgName = orgNameList.map((item) => item.dept_name).join("/");

        form.setFieldValue("orgName", orgName);
        const params = flowItems.find((item2) => item2.jobId === item.jobId);
        if (params) {
            params.orgName = form.getFieldValue("orgName");
        }
        handleOrgChange(form.getFieldValue("orgName"));
    };

    const disabled = useMemo(() => {
        return mode === "editParentJob" || mode === "editChildrenJob";
    }, [mode]);

    const disabledParent = useMemo(() => {
        return mode === "editParentJob";
    }, [mode]);

    const finalAssignmentList = useMemo(() => {
        if (mode === "editParentJob") {
            return assignmentList.filter((item) => item.value !== ASSIGNMENT_TYPE_MANUAL);
        }
        return assignmentList;
    }, []);

    return (
        <div className={styles["flow-item-wrapper"]}>
            <Form name={item.jobId} form={form} initialValues={item} layout="vertical">
                <div className={styles["flow-item"]}>
                    <Form.Item name="jobId" hidden>
                        <Input />
                    </Form.Item>
                    <Form.Item name="orgId" label="需求部门" rules={[{ required: true }]}>
                        <Cascader
                            placeholder="请选择需求部门"
                            disabled={mode === "publish" || disabled}
                            options={departmentList}
                            changeOnSelect
                            fieldNames={{
                                label: "dept_name",
                                value: "dept_code",
                            }}
                            onChange={handleChangeOrg}
                            onBlur={() =>
                                getJobList(form.getFieldValue("orgId")[form.getFieldValue("orgId").length - 1])
                            }
                            showSearch={{ limit: Infinity }}
                            allowClear
                            expandTrigger="hover"
                            optionRender={(option) => {
                                return (
                                    <div
                                        onClick={(e) => {
                                            // 禁用节点，禁止点击后选中
                                            if (option.isDisabled) {
                                                e.stopPropagation();
                                            }
                                        }}
                                        style={{
                                            color: option.isDisabled ? "var(--sub-text-color-2)" : "inherit",
                                            cursor: option.isDisabled ? "not-allowed" : "pointer",
                                        }}
                                    >
                                        {option.dept_name}
                                    </div>
                                );
                            }}
                        />
                    </Form.Item>
                    <Form.Item name="orgName" hidden>
                        <Input />
                    </Form.Item>
                    <Form.Item name="jobNameInnerPath" label="职位名称" rules={[{ required: true }]}>
                        <Cascader
                            placeholder="请选择职位名称"
                            disabled={mode === "publish" || disabled}
                            options={currentJobList}
                            fieldNames={{
                                label: "desc",
                                value: "code",
                            }}
                            showSearch={{ limit: Infinity }}
                            allowClear
                            onChange={handleChangeJobNameInner}
                            expandTrigger="hover"
                        />
                    </Form.Item>
                    <Form.Item name="jobNameInner" hidden>
                        <Input />
                    </Form.Item>
                    <Form.Item
                        name="jobNameOuter"
                        label="对外职位名称"
                        rules={[
                            { required: true },
                            {
                                validator: (rule, value) => {
                                    const regex = /【26校招(\s*-\s*[^\s】][^】]*)?】.*/;

                                    if (!regex.test(value)) {
                                        return Promise.reject(
                                            new Error(
                                                "对外职位名称格式为【26校招 - xxxx】岗位名称或者【26校招】岗位名称"
                                            )
                                        );
                                    }
                                    return Promise.resolve();
                                },
                            },
                        ]}
                    >
                        <Input maxLength={100} placeholder="请输入对外职位名称" allowClear />
                    </Form.Item>
                    <Form.Item name="hiringBatch" label="招聘批次" rules={[{ required: true }]}>
                        <Select
                            placeholder="请选择招聘批次"
                            disabled={mode === "publish" || disabled}
                            options={hiringBatchData}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item name="hiringPersonNum" label="目标人数" rules={[{ required: true }]}>
                        <InputNumber
                            placeholder="请输入目标人数"
                            disabled={mode === "publish" || disabledParent}
                            min={1}
                            max={300}
                            changeOnWheel
                            precision={0}
                        />
                    </Form.Item>
                    <Form.Item
                        name="offerTarget"
                        label="offer目标(建议：目标人数*3)"
                        rules={[
                            { required: true },
                            ({ getFieldValue }) => ({
                                validator(_, value) {
                                    const hiringNum = getFieldValue("hiringPersonNum");
                                    if (!value || !hiringNum) {
                                        return Promise.resolve();
                                    }

                                    if (value >= hiringNum) {
                                        return Promise.resolve();
                                    }

                                    return Promise.reject(new Error(`Offer目标必须大于目标人数 ${hiringNum}`));
                                },
                            }),
                        ]}
                        dependencies={["hiringPersonNum"]}
                    >
                        <InputNumber
                            placeholder="请输入offer目标"
                            disabled={mode === "publish" || disabledParent}
                            min={1}
                            changeOnWheel
                            precision={0}
                            onChange={() => {}}
                        />
                    </Form.Item>
                    <Form.Item name="jobType" label="职位类别" rules={[{ required: true }]}>
                        <Select disabled={disabled} placeholder="请选择职位类别" options={jobTypeData} allowClear />
                    </Form.Item>
                    <Form.Item
                        name="reason"
                        label="原因说明"
                        dependencies={["jobType"]}
                        rules={[
                            ({ getFieldValue }) => ({
                                required: getFieldValue("jobType") === "核心岗位",
                            }),
                        ]}
                    >
                        <TextArea
                            disabled={disabled}
                            maxLength={500}
                            placeholder="请输入原因说明"
                            autoSize={{ minRows: 1, maxRows: 3 }}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item name="topTalents" label="拔尖人才" rules={[{ required: true }]}>
                        <InputNumber
                            disabled={disabledParent}
                            placeholder="请输入拔尖人才百分比"
                            min={0}
                            max={100}
                            changeOnWheel
                            precision={2}
                            addonAfter="%"
                        />
                    </Form.Item>
                    <Form.Item name="excellentTalents" label="优秀人才" rules={[{ required: true }]}>
                        <InputNumber
                            disabled={disabledParent}
                            placeholder="请输入优秀人才百分比"
                            min={0}
                            max={100}
                            changeOnWheel
                            precision={2}
                            addonAfter="%"
                        />
                    </Form.Item>
                    <Form.Item name="regularTalents" label="普通人才" rules={[{ required: true }]}>
                        <InputNumber
                            disabled={disabledParent}
                            placeholder="请输入普通人才百分比"
                            min={0}
                            max={100}
                            changeOnWheel
                            precision={2}
                            addonAfter="%"
                        />
                    </Form.Item>
                    <Form.Item
                        name="jobLocation"
                        label="工作地点"
                        rules={[
                            { required: true },
                            () => ({
                                validator(_, value) {
                                    for (const item of value) {
                                        if (!jobLocationData.find((item2) => item === item2.value)) {
                                            return Promise.reject(new Error(`${item}不存在`));
                                        }
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <Select
                            showSearch
                            mode="multiple"
                            placeholder="请选择工作地点"
                            disabled={mode === "publish" || disabledParent}
                            // disabled={mode === "publish" || disabled}
                            options={jobLocationData}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item
                        name="educational"
                        label="学历要求"
                        rules={[
                            { required: true },
                            () => ({
                                validator(_, value) {
                                    for (const item of value) {
                                        if (!educationList.find((item2) => item === item2)) {
                                            return Promise.reject(new Error(`${item}不存在`));
                                        }
                                    }

                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <Select
                            mode="multiple"
                            placeholder="请选择学历要求"
                            disabled={mode === "publish" || disabledParent}
                            // disabled={mode === "publish" || disabled}
                            options={educationList.map((item) => ({
                                label: item,
                                value: item,
                            }))}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item name="language" label="语言能力要求">
                        <Select
                            disabled={disabled}
                            placeholder="请选择语言能力要求"
                            options={languageList}
                            allowClear
                        />
                    </Form.Item>
                    {(mode === "publish" || mode === "editParentJob" || allocationMethod) && (
                        <Form.Item name="assignment" label="分配方式" rules={[{ required: true }]}>
                            <Select
                                disabled={mode === "editChildrenJob"}
                                // disabled={disabled}
                                placeholder="请选择分配方式"
                                options={finalAssignmentList}
                                allowClear
                                onChange={handleAssignmentChange}
                            />
                        </Form.Item>
                    )}
                </div>
                <div style={{ marginBottom: 12 }}>{customChildren}</div>
                <div className={styles["job-jd-wrapper"]}>
                    <Form.Item label="岗位JD描述">
                        <RichTextEditor
                            id={`${item.jobId}_RichTextEditor`}
                            ref={richTextRef}
                            modules={{
                                toolbar: [
                                    [{ header: [1, 2, 3, 4, 5, 6, false] }],
                                    ["bold", "italic", "underline", "strike"],
                                    [{ color: [] }, { background: [] }],
                                    [{ align: [] }],
                                    [{ list: "ordered" }, { list: "bullet" }],
                                ],
                                clipboard: {
                                    matchVisual: false,
                                },
                            }}
                            placeholder="请输入岗位JD描述"
                            height="250px"
                        />
                    </Form.Item>
                    <div style={{ display: "flex", justifyContent: "flex-end", marginTop: "12px", gap: "12px" }}>
                        <Button
                            color="lime"
                            variant="solid"
                            loading={AIButtonLoading}
                            onClick={async () => {
                                setAIButtonLoading(true);
                                handleChangeLoading(true);
                                const params = cloneDeep(form.getFieldsValue());
                                params.jobNameInnerPath = Array.isArray(params.jobNameInnerPath)
                                    ? params.jobNameInnerPath.join("/")
                                    : params.jobNameInnerPath;
                                params.orgId = Array.isArray(params.orgId) ? params.orgId.join("/") : params.orgId;
                                params.educational = Array.isArray(params.educational)
                                    ? params.educational.join("/")
                                    : params.educational;
                                params.jobLocation = Array.isArray(params.jobLocation)
                                    ? params.jobLocation.join("/")
                                    : params.jobLocation;
                                params.jobJD = richTextRef.current?.getText();
                                params.jobTags = null;

                                let tags: TagsResp[] = [];
                                let jdContent = "";
                                setJobJDByAI("");

                                try {
                                    getAIMessageApi(
                                        params,
                                        (message: StreamMessage) => {
                                            if (message.id === "task.job.spec.text") {
                                                setJobJDByAI((prev) => prev + message.content);
                                                jdContent += message.content;
                                            }
                                        },
                                        () => {
                                            handleChangeLoading(false);
                                            setAIButtonLoading(false);
                                        },
                                        async () => {
                                            form.setFieldValue("jobJDByAI", jdContent);
                                            const htmlContent = new markdownIt().render(jdContent);
                                            const jobJD = formatHtmlWithLists(htmlContent);
                                            handleChangeLoading(true);

                                            try {
                                                const res: RespParams<TagsResp[]> = await (
                                                    await getAITagsApi(jobJD)
                                                ).json();
                                                if (res.code === 200) {
                                                    tags = res.data;
                                                    if (tags.length > 0) {
                                                        // 前端限制tags数量不超出15个
                                                        tags = tags.slice(0, 15);
                                                    }
                                                }
                                            } catch (error) {
                                                tags = [];
                                                jdContent = "";
                                            }
                                            const newTags = cloneDeep([...JobTags]);
                                            if (tags && tags.length > 0) {
                                                // 生成标签，更新选中标签
                                                newTags?.forEach((tag) => {
                                                    tag?.children?.forEach((tag2: any) => {
                                                        const param = tags.filter(
                                                            (item) =>
                                                                item.layer === tag.label && item.label === tag2.label
                                                        );
                                                        if (param && param.length > 0) {
                                                            tag2.value.push(...param);
                                                        }
                                                    });
                                                });
                                                // 编辑模式下进行tags更新，包括待办列表入口，招聘需求列表入口
                                                if (mode?.includes("edit") || mode === "publish") {
                                                    item.jobTags = newTags;
                                                } else {
                                                    flowItems.forEach((item2) => {
                                                        if (item2.jobId === item.jobId) {
                                                            item2.jobTags = newTags;
                                                        }
                                                    });
                                                }
                                            }
                                            handleChangeLoading(false);
                                            setAIButtonLoading(false);
                                        }
                                    );
                                } catch (err) {
                                    console.error("err", err);
                                    handleChangeLoading(false);
                                }
                            }}
                        >
                            AI优化
                        </Button>
                    </div>
                    <Form.Item
                        shouldUpdate={(prevValues, currentValues) => prevValues.jobJDByAI !== currentValues.jobJDByAI}
                    >
                        {() => {
                            return jobJDByAI ? (
                                <div>
                                    {!AIButtonLoading && (
                                        <div style={{ margin: "12px 0", display: "flex", justifyContent: "flex-end" }}>
                                            <Button
                                                color="lime"
                                                variant="solid"
                                                onClick={() => {
                                                    const htmlContent = new markdownIt().render(jobJDByAI);
                                                    richTextRef.current?.setHTML(htmlContent as string);
                                                }}
                                            >
                                                一键应用
                                            </Button>
                                        </div>
                                    )}
                                    <Form.Item label="AI优化结果">
                                        <MDXMarkdown content={jobJDByAI} />
                                    </Form.Item>
                                </div>
                            ) : null;
                        }}
                    </Form.Item>
                </div>
                <Form.Item name="jobTags" hidden>
                    <Input />
                </Form.Item>
                <JobTagsPage
                    tagsList={item?.jobTags ?? []}
                    handleChangeTags={(tags) => {
                        form.setFieldsValue({
                            jobTags: tags,
                        });
                    }}
                />
            </Form>
        </div>
    );
});

FlowItem.displayName = "FlowItem";

export default FlowItem;
