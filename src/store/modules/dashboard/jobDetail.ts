import { create } from "zustand";
import {
    getFlowStageList<PERSON>pi,
    getFlowStatusApi,
    getJobFlowDetailByChildrenJobIdApi,
} from "@/request/modules/dashboard";
import { FormInstance } from "antd";
import { RespUser } from "@/components/typing";

export interface AddInterviewExpose {
    init: (record?: any) => void;
    form: FormInstance;
    selectedUserData: RespUser[];
}

export interface JobDetailExpose {
    init: (batchJobList?: BatchJobResp[], type?: string) => void;
}

export interface InterviewItemReq {
    stageName: string;
    stageOrder: number;
    interviewerIds: string[];
    beisenTemplateId: string;
}

export interface InterviewReq {
    jobSpecIds: number[];
    interviewStages: InterviewItemReq[];
}

export interface InterviewItemResp extends InterviewItemReq {
    id: number;
    interviewerNames: string[];
    interviewers: string;
}

export interface InterviewResp {
    id: number;
    jobSpecId: number;
    interviewStagesDto: InterviewItemResp[];
}

export interface UpdateInterviewItemReq extends InterviewItemReq {
    stageIds: number[];
}

export interface BatchJobResp {
    jobSpecId: number;
    stageIds: number[];
}

/**
 * 流程数据操作类型
 */
export type FlowAction =
    | { type: "UPDATE_STAGE"; payload: { stageCode: string; data: any } }
    | { type: "ADD_STAGE"; payload: any }
    | { type: "DELETE_STAGE"; payload: { stageCode: string } }
    | { type: "REORDER_STAGES"; payload: { fromIndex: number; toIndex: number } }
    | { type: "UPDATE_STAGE_STATUS"; payload: { stageCode: string; statusCode: string; data: any } }
    | { type: "ADD_STATUS"; payload: { stageCode: string; newStatus: any } }
    | { type: "DELETE_STAGE_STATUS"; payload: { stageCode: string; statusCode: string } }
    | { type: "REORDER_STATUSES"; payload: { stageCode: string; fromIndex: number; toIndex: number } };

export interface ProcessDict {
    code: string;
    name: string;
    description: string;
}

export interface StageDict {
    code: string;
    name: string;
    description: string;
    type: string;
}

export interface StateDict {
    code: string;
    name: string;
    description: string;
    type: string;
}

export interface State {
    id?: string;
    dictId: string;
    order?: number;
    lockType: number | null;
    unlockDays: number | null;
    outerName: string;
    requireReason: boolean;
    trackStateDict: StateDict[];
}
export interface Stage {
    id?: string;
    dictId: string;
    order?: number;
    handlers: string[];
    handlersEntity: { empId: string; empName: string }[];
    stageDict: StageDict[];
    states: State[];
}

/**
 * 面试流程详情
 */
export interface InterviewFlowDetail {
    id: string;
    dictId: string;
    jobSpecId: string;
    processDict: ProcessDict;
    stage: Stage[];
}

/**
 * 岗位详情状态接口
 */
export interface JobDetailState {
    flowDetail: InterviewFlowDetail | null;
    detailInfo: any | null;
    loading: boolean;
    error: string | null;
    stageList: any[];
    statusList: any[];
    // 数据获取方法
    getFlowDetail: (jobSpecId: number) => Promise<void>;
    // 统一的数据修改方法
    dispatch: (action: FlowAction) => void;
    // 批量操作方法
    batchUpdate: (actions: FlowAction[]) => void;
    // 重置和清理方法
    clearError: () => void;
    resetFlowDetail: () => void;
    getStageList: () => Promise<void>;
    getStatusList: () => Promise<void>;
    getFinalFlowDetail: () => InterviewFlowDetail | null;
    setDetailInfo: (info: any) => void;
}

/**
 * 流程数据reducer
 * @param state 当前状态
 * @param action 操作类型
 * @returns 新状态
 */
const flowReducer = (state: any, action: FlowAction): InterviewFlowDetail | null => {
    if (!state) return null;

    switch (action.type) {
        case "UPDATE_STAGE": {
            const { stageCode, data } = action.payload;
            return {
                ...state,
                stage:
                    state.stage?.map((stage: any) => (stage.dictId === stageCode ? { ...stage, ...data } : stage)) ||
                    [],
            };
        }

        case "ADD_STAGE": {
            const pendingList = state.stage.filter((item: any) => item?.stageDict?.type !== "5");
            const finalList = state.stage.filter((item: any) => item?.stageDict?.type === "5");

            return {
                ...state,
                stage: [...pendingList, action.payload, ...finalList],
            };
        }
        case "ADD_STATUS": {
            const { stageCode, newStatus } = action.payload;
            return {
                ...state,
                stage: [...(state.stage || [])].map((stage) =>
                    stage.dictId === stageCode ? { ...stage, states: [...(stage.states || []), newStatus] } : stage
                ),
            };
        }

        case "DELETE_STAGE": {
            const { stageCode } = action.payload;
            return {
                ...state,
                stage: state.stage?.filter((stage: any) => stage.dictId !== stageCode) || [],
            };
        }

        case "REORDER_STAGES": {
            const { fromIndex, toIndex } = action.payload;
            const newStages = [...(state.stage || [])];
            const [movedStage] = newStages.splice(fromIndex, 1);
            newStages.splice(toIndex, 0, movedStage);
            return {
                ...state,
                stage: newStages,
            };
        }

        case "REORDER_STATUSES": {
            const { stageCode, fromIndex, toIndex } = action.payload;
            const newStatuses = [...(state.stage || [])];
            const stage = newStatuses.find((stage: any) => stage.dictId === stageCode);
            const [movedStatus] = stage.states.splice(fromIndex, 1);
            stage.states.splice(toIndex, 0, movedStatus);
            return {
                ...state,
                stage: newStatuses,
            };
        }

        case "UPDATE_STAGE_STATUS": {
            const { stageCode, statusCode, data } = action.payload;
            return {
                ...state,
                stage:
                    state.stage?.map((stage: any) =>
                        stage.dictId === stageCode
                            ? {
                                  ...stage,
                                  states: stage.states?.map((status: any) =>
                                      status.dictId === statusCode ? { ...status, ...data } : status
                                  ),
                              }
                            : stage
                    ) || [],
            };
        }

        case "DELETE_STAGE_STATUS": {
            const { stageCode, statusCode } = action.payload;
            return {
                ...state,
                stage:
                    state.stage?.map((stage: any) =>
                        stage.dictId === stageCode
                            ? {
                                  ...stage,
                                  states: stage.states?.filter((status: any) => status.dictId !== statusCode) || [],
                              }
                            : stage
                    ) || [],
            };
        }

        default:
            return state;
    }
};

/**
 * 岗位详情状态管理store
 * @returns JobDetailState 岗位详情状态和方法
 */
const useJobDetailStore = create<JobDetailState>((set, get) => {
    return {
        flowDetail: null,
        detailInfo: null,
        loading: false,
        error: null,
        stageList: [],
        statusList: [],

        /**
         * 获取流程详情
         * @param jobSpecId 岗位ID
         */
        getFlowDetail: async (jobSpecId: number) => {
            set({ loading: true, error: null });
            try {
                const response = await getJobFlowDetailByChildrenJobIdApi(jobSpecId);
                const result: any = await response.json();

                if (result.code === 200) {
                    set({
                        flowDetail: result.data,
                        loading: false,
                    });
                } else {
                    set({
                        error: result.message || "获取流程详情失败",
                        loading: false,
                    });
                }
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : "网络请求失败",
                    loading: false,
                });
            }
        },
        /**
         * 获取阶段列表
         */
        getStageList: async () => {
            try {
                const response = await getFlowStageListApi();
                const result: any = await response.json();

                if (result.code === 200) {
                    set({
                        stageList: result.data,
                    });
                }
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : "网络请求失败",
                });
            }
        },

        /**
         * 获取状态列表
         */
        getStatusList: async () => {
            try {
                const response = await getFlowStatusApi();
                const result: any = await response.json();

                if (result.code === 200) {
                    set({
                        statusList: result.data,
                    });
                }
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : "网络请求失败",
                });
            }
        },

        /**
         * 统一的数据修改方法
         * @param action 操作类型
         */
        dispatch: (action: FlowAction) => {
            const { flowDetail } = get();
            const newFlowDetail = flowReducer(flowDetail, action);
            set({ flowDetail: newFlowDetail });
        },

        /**
         * 批量操作方法
         * @param actions 操作数组
         */
        batchUpdate: (actions: FlowAction[]) => {
            const { flowDetail } = get();
            let newFlowDetail = flowDetail;

            actions.forEach((action) => {
                newFlowDetail = flowReducer(newFlowDetail, action);
            });

            set({ flowDetail: newFlowDetail });
        },

        /**
         * 清除错误信息
         */
        clearError: () => {
            set({ error: null });
        },

        /**
         * 重置流程详情
         */
        resetFlowDetail: () => {
            set({ flowDetail: null, error: null, loading: false });
        },

        /**
         * 获取最终的流程详情
         * @returns InterviewFlowDetail 最终的流程详情
         */
        getFinalFlowDetail: () => {
            const { flowDetail } = get();
            if (!flowDetail) {
                return null;
            }
            // 深拷贝flowDetail以避免修改原始数据
            const finalFlowDetail = JSON.parse(JSON.stringify(flowDetail)) as InterviewFlowDetail;
            // 为stage循环赋值order字段
            if (finalFlowDetail.stage && Array.isArray(finalFlowDetail.stage)) {
                finalFlowDetail.stage.forEach((stage, stageIndex) => {
                    stage.order = stageIndex + 1;

                    // 为每个stage下的states循环赋值order字段
                    if (stage.states && Array.isArray(stage.states)) {
                        stage.states.forEach((state, stateIndex) => {
                            state.order = stateIndex + 1;
                        });
                    }
                });
            }
            return finalFlowDetail;
        },
        setDetailInfo: (detailInfo: any) => {
            set({ detailInfo });
        },
    };
});

export default useJobDetailStore;
