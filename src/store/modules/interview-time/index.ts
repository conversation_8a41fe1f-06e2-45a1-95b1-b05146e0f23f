import { create } from "zustand";
import http from "@/request";
import { RespParams } from "@/types/typing";

export enum PeriodType {
    FreeTime = "A", // 空闲日程
    Interview = "B", // 面试日程
}

export enum InterviewStatus {
    NotStart = "A", // 未开始
    NotResult = "B", // 未评价
    Completed = "C", // 已完成
}

// 获取公司节假日请求体
export interface GetCompanyHolidayRequest {
    startTime: string; // 开始时间
    endTime: string; // 结束时间
}

// 公司节假日
interface CompanyHoliday {
    deleted: boolean;
    gmt_create: string;
    gmt_modified: string;
    id: string;
    start_time: string;
    end_time: string;
    summary: string;
    description: string;
    adjusted: boolean;
}

// 获取公司节假日响应体
export interface GetCompanyHolidayResponse {
    companyHolidayList: CompanyHoliday[];
}

// 获取用户可以操作的日期请求体
export interface GetUserOperableDateRequest {
    from: number; // 开始时间
    threshold: string; // 结束时间
}

// 获取用户可以操作的日期响应体
export type GetUserOperableDateResponse = string[];

// 查询日程的请求体
export interface QueryScheduleRequest {
    startTime: string; // 开始时间
    endTime: string; // 结束时间
    specificId: string | null; // 日程ID
    targetEmpId: string[]; // 所属人工号
    period: PeriodType | ""; // 日程类型
}

// 日程条目
export interface ScheduleItem {
    deleted: boolean;
    gmt_create: string;
    gmt_modified: string;
    id: string;
    itr_emp_id: string;
    feishu_id: string;
    gmt_start: string;
    gmt_end: string;
    period: PeriodType;
    released: boolean;
}

// 员工信息
export interface EmployeeInfo {
    opr_id: string;
    name: string;
    avatar: string;
    emp_id: string;
}

export interface FeishuSchduleItem {
    gmtEnd: string;
    gmtStart: string;
    summary: string;
    description: string;
    appLink: string;
    feishuId: string;
    meetingLink: string;
}

// 员工日程
export interface EmployeeSchedule {
    feishu_items: FeishuSchduleItem[];
    items: ScheduleItem[];
    info: EmployeeInfo;
}

// 查询日程的响应体
export type QueryScheduleResponse = Record<string, EmployeeSchedule>;

/**
 * 创建日程的请求体
 */
export interface CreateScheduleRequest {
    gapStart: string; // "2025-06-28 09:00:00"
    gapEnd: string; // "2025-06-28 09:00:00"
    reminderId: string | null;
}

/**
 * 已保存的日程信息
 */
export interface SavedSchedule {
    gmt_create: string;
    gmt_modified: string;
    id: string;
    itr_emp_id: string;
    gmt_start: string;
    gmt_end: string;
    period: string;
    released: boolean;
}

/**
 * 创建日程接口返回的数据结构
 */
export interface ScheduleCreationData {
    saved: SavedSchedule;
    affectedReminders: any[]; // TODO:`affectedReminders` 类型不明确，暂时使用 any[]
}

// 修改日程的请求体
export interface UpdateScheduleRequest {
    id: string;
    gapStart: string;
    gapEnd: string;
    reminderId: string | null;
}

// 修改日程的响应体
export interface UpdateScheduleResponse extends ScheduleCreationData {}

// 删除日程的请求体
export interface DeleteScheduleRequest {
    id: string;
    gapStart: string;
    gapEnd: string;
}

// 删除日程的响应体
export interface DeleteScheduleResponse extends ScheduleCreationData {}

// 查看日程的详情请求体
export interface ViewScheduleDetailRequest {
    feishuEventId: string;
}

// 查看日程的详情响应体
export interface ViewScheduleDetailResponse {
    deleted: boolean;
    createTime: string;
    modifiedTime: string;
    id: string;
    itrEmpId: string;
    gmtStart: string;
    gmtEnd: string;
    summary: string;
    description: string;
    assginerEmpId: string;
    video: boolean;
    attendees: string[];
    evalTempId: string;
    itvResult: string;
    hookCandidateNames: Record<string, string>;
    meetingLink: string;
    appLink: string;
    avatar: string;
    name: string;
}

/**
 * 面试时间相关的状态管理
 */
export interface InterviewTimeStore {
    id: string;
    /**
     * 创建一个新的面试日程
     * @param data 创建日程所需的参数
     * @returns 创建成功后的日程信息
     */
    createSchedule: (data: CreateScheduleRequest) => Promise<ScheduleCreationData>;
    /**
     * 查询日程
     * @param data 查询日程所需的参数
     * @returns 查询成功后的日程信息
     */
    querySchedule: (data: QueryScheduleRequest) => Promise<QueryScheduleResponse>;
    /**
     * 修改日程
     * @param data 修改日程所需的参数
     * @returns 修改成功后的日程信息
     */
    updateSchedule: (data: UpdateScheduleRequest) => Promise<ScheduleCreationData>;
    /**
     * 删除日程
     * @param data 删除日程所需的参数
     * @returns 删除成功后的日程信息
     */
    deleteSchedule: (data: DeleteScheduleRequest) => Promise<DeleteScheduleResponse>;
    /**
     * 获取公司节假日
     * @param data 获取公司节假日所需的参数
     * @returns 获取公司节假日成功后的数据
     */
    getCompanyHoliday: (data: GetCompanyHolidayRequest) => Promise<GetCompanyHolidayResponse>;
    /**
     * 获取用户可操作的日期
     * @param data 获取用户可操作的日期所需的参数
     * @returns 获取用户可操作的日期成功后的数据
     */
    getUserOperableDate: (data: GetUserOperableDateRequest) => Promise<GetUserOperableDateResponse>;
    // 查看日程的详情
    viewScheduleDetail: (data: ViewScheduleDetailRequest) => Promise<ViewScheduleDetailResponse>;
}

const useInterviewTimeStore = create<InterviewTimeStore>((set) => ({
    id: "", // id 的初始状态
    createSchedule: async (data: CreateScheduleRequest) => {
        try {
            const response = await http.post("schedule/interview/spare/create", data);
            const result = (await response.json()) as RespParams<ScheduleCreationData>;
            return result.data;
        } catch (error) {
            console.error("创建日程时发生错误:", error);
            throw error;
        }
    },
    querySchedule: async (data: QueryScheduleRequest) => {
        try {
            const response = await http.post("schedule/fetch", data);
            const result = (await response.json()) as RespParams<QueryScheduleResponse>;
            return result.data;
        } catch (error) {
            console.error("查询日程时发生错误:", error);
            throw error;
        }
    },
    updateSchedule: async (data: UpdateScheduleRequest) => {
        try {
            const response = await http.patch("schedule/interview/spare/patch", data);
            const result = (await response.json()) as RespParams<UpdateScheduleResponse>;
            return result.data;
        } catch (error) {
            console.error("修改日程时发生错误:", error);
            throw error;
        }
    },
    deleteSchedule: async (data: DeleteScheduleRequest) => {
        try {
            const response = await http.delete("schedule/interview/spare/delete", data);
            const result = (await response.json()) as RespParams<ScheduleCreationData>;
            return result.data;
        } catch (error) {
            console.error("删除日程时发生错误:", error);
            throw error;
        }
    },
    getCompanyHoliday: async (data: GetCompanyHolidayRequest) => {
        try {
            const response = await http.get("schedule/calendar/fetch", data);
            const result = (await response.json()) as RespParams<GetCompanyHolidayResponse>;
            return result.data;
        } catch (error) {
            console.error("获取公司节假日时发生错误:", error);
            throw error;
        }
    },
    getUserOperableDate: async (data: GetUserOperableDateRequest) => {
        try {
            const response = await http.get("schedule/calendar/fetch/next-n", data);
            const result = (await response.json()) as RespParams<GetUserOperableDateResponse>;
            return result.data;
        } catch (error) {
            console.error("获取用户可操作日期时发生错误:", error);
            throw error;
        }
    },
    viewScheduleDetail: async (data: ViewScheduleDetailRequest) => {
        try {
            const response = await http.get("schedule/interview/fetch/event", data);
            const result = (await response.json()) as RespParams<ViewScheduleDetailResponse>;
            return result.data;
        } catch (error) {
            console.error("查询日程详情发生错误:", error);
            throw error;
        }
    },
}));

export default useInterviewTimeStore;
