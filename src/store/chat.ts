import { getMessageTextContent, isDalle3, safeLocalStorage, trimTopic } from "@/utils/utils";

import { indexedDBStorage } from "@/utils/indexedDB-storage";
import { nanoid } from "nanoid";
import type { ClientApi, MultimodalContent, RequestMessage } from "@/client/api";
import { getClientApi } from "@/client/api";
import { ChatControllerPool } from "@/client/controller";
// import { showToast } from "@/components/ui-lib";
import {
    DEFAULT_INPUT_TEMPLATE,
    DEFAULT_MODELS,
    DEFAULT_SYSTEM_TEMPLATE,
    DEEPSEEK_SUMMARIZE_MODEL,
    KnowledgeCutOffDate,
    ServiceProvider,
    StoreKey,
} from "@/constant";
import Locale, { getLang } from "@/locales";
import { prettyObject } from "@/utils/format";
import { createPersistStore } from "@/utils/store";
import { estimateTokenLength } from "@/utils/token";
import { ModelConfig, ModelType, useAppConfig } from "./config";

const localStorage = safeLocalStorage();

/**
 * 聊天消息中的工具信息
 */
export type ChatMessageTool = {
    /** 工具调用的唯一 ID */
    id: string;
    /** 索引，可选 */
    index?: number;
    /** 工具类型，可选 */
    type?: string;
    /** 函数调用详情 */
    function?: {
        /** 函数名称 */
        name: string;
        /** 函数参数，JSON 字符串 */
        arguments?: string;
    };
    /** 工具输出内容，可选 */
    content?: string;
    /** 是否发生错误，可选 */
    isError?: boolean;
    /** 错误信息，可选 */
    errorMsg?: string;
};

/**
 * 聊天消息对象类型
 */
export type ChatMessage = RequestMessage & {
    /** 消息发送日期时间字符串 */
    date: string;
    /** 是否为流式响应消息 */
    streaming?: boolean;
    /** 是否为错误消息 */
    isError?: boolean;
    /** 消息唯一 ID */
    id: string;
    /** 消息所使用的模型类型 */
    model?: ModelType;
    /** 消息关联的工具调用信息 */
    tools?: ChatMessageTool[];
    /** 语音消息 URL，可选 */
    audio_url?: string;
};

/**
 * 创建一条新的聊天消息
 * @param override 覆盖默认值的消息属性
 * @returns 创建的聊天消息对象
 */
export function createMessage(override: Partial<ChatMessage>): ChatMessage {
    return {
        id: nanoid(),
        date: new Date().toLocaleString(),
        role: "user",
        content: "",
        ...override,
    };
}

/**
 * 聊天统计信息接口
 */
export interface ChatStat {
    /** Token 数量 */
    tokenCount: number;
    /** 词语数量 */
    wordCount: number;
    /** 字符数量 */
    charCount: number;
}

/**
 * 聊天会话接口
 */
export interface ChatSession {
    /** 会话唯一 ID */
    id: string;
    /** 会话主题 */
    topic: string;
    /** 记忆提示语 */
    memoryPrompt: string;
    /** 消息列表 */
    messages: ChatMessage[];
    /** 聊天统计信息 */
    stat: ChatStat;
    /** 最后更新时间戳 */
    lastUpdate: number;
    /** 最后总结的消息索引 */
    lastSummarizeIndex: number;
    /** 清除上下文的消息索引，可选 */
    clearContextIndex?: number;
    /** 角色头像 */
    avatar: string;
    /** 上下文 */
    context: ChatMessage[];
    /** 模型配置 */
    modelConfig: ModelConfig;
    /** 插件 */
    plugin: string[];
}

/** 默认会话主题 */
export const DEFAULT_TOPIC = Locale.Store.DefaultTopic;
// /** 机器人欢迎消息 */
// export const BOT_HELLO: ChatMessage = createMessage({
//     role: "assistant",
//     content: Locale.Store.BotHello,
// });

/**
 * 创建一个空的聊天会话
 * @returns 空的聊天会话对象
 */
function createEmptySession(): ChatSession {
    return {
        id: nanoid(),
        topic: DEFAULT_TOPIC,
        memoryPrompt: "",
        messages: [],
        stat: {
            tokenCount: 0,
            wordCount: 0,
            charCount: 0,
        },
        lastUpdate: Date.now(),
        lastSummarizeIndex: 0,
        avatar: "gpt-bot",
        context: [],
        modelConfig: { ...useAppConfig.getState().modelConfig },
        plugin: [],
    };
}

/**
 * 根据当前模型和提供商确定用于总结的模型
 * @param currentModel 当前使用的模型名称
 * @param providerName 当前模型提供商名称
 * @returns [总结模型名称, 总结模型提供商名称]
 */
function getSummarizeModel(currentModel: string, providerName: string): string[] {
    // 如果是 DeepSeek 模型，使用特定的 DeepSeek 总结模型
    if (currentModel.startsWith("deepseek-")) {
        return [DEEPSEEK_SUMMARIZE_MODEL, ServiceProvider.DeepSeek];
    }

    // 默认情况下，使用当前模型进行总结
    return [currentModel, providerName];
}

/**
 * 计算消息列表的总 Token 长度
 * @param msgs 消息列表
 * @returns 总 Token 长度
 */
function countMessages(msgs: ChatMessage[]) {
    return msgs.reduce((pre, cur) => pre + estimateTokenLength(getMessageTextContent(cur)), 0);
}

/**
 * 使用模型配置填充输入模板
 * @param input 用户输入内容
 * @param modelConfig 当前模型的配置
 * @returns 填充了变量的模板字符串
 */
function fillTemplateWith(input: string, modelConfig: ModelConfig) {
    // 获取模型的知识截止日期
    const cutoff = KnowledgeCutOffDate[modelConfig.model] ?? KnowledgeCutOffDate.default;
    // 查找模型信息以获取提供商名称
    const modelInfo = DEFAULT_MODELS.find((m) => m.name === modelConfig.model);

    // Fix: 使用 let 替代 var
    let serviceProvider = "OpenAI"; // 默认为 OpenAI
    if (modelInfo) {
        // TODO: 根据 modelConfig.model 自动检测 providerName
        // 直接使用 modelInfo 中的 providerName
        serviceProvider = modelInfo.provider.providerName;
    }

    // 定义模板变量
    const vars = {
        ServiceProvider: serviceProvider,
        cutoff,
        model: modelConfig.model,
        time: new Date().toString(),
        lang: getLang(),
        input: input,
    };

    // 获取模型配置中的模板，若无则使用默认输入模板
    let output = modelConfig.template ?? DEFAULT_INPUT_TEMPLATE;

    // 移除重复的前缀（如果输入已经包含了模板内容）
    if (input.startsWith(output)) {
        output = "";
    }

    // 确保模板中包含 {{input}} 变量
    const inputVar = "{{input}}";
    if (!output.includes(inputVar)) {
        output += "\n" + inputVar;
    }

    // 替换模板中的所有变量
    Object.entries(vars).forEach(([name, value]) => {
        const regex = new RegExp(`{{${name}}}`, "g");
        output = output.replace(regex, value.toString()); // 确保替换值为字符串
    });

    return output;
}

/** 默认聊天状态 */
const DEFAULT_CHAT_STATE = {
    /** 会话列表 */
    sessions: [createEmptySession()],
    /** 当前选中的会话索引 */
    currentSessionIndex: 0,
    /** 上次用户输入内容 */
    lastInput: "",
};

/**
 * Zustand store 用于管理聊天状态
 */
export const useChatStore = createPersistStore(
    DEFAULT_CHAT_STATE,
    (set, _get) => {
        /** 获取 store 的当前状态和方法 */
        function get() {
            return {
                ..._get(),
                ...methods,
            };
        }

        /** Store 的方法集合 */
        const methods = {
            /**
             * 基于当前会话创建一个新的分支会话
             */
            forkSession() {
                const currentSession = get().currentSession();
                if (!currentSession) return; // 如果没有当前会话，则退出

                // 创建一个新的空会话
                const newSession = createEmptySession();

                // 复制主题
                newSession.topic = currentSession.topic;
                // 深拷贝消息列表，并为每条消息生成新的 ID
                newSession.messages = currentSession.messages.map((msg) => ({
                    ...msg,
                    id: nanoid(), // 生成新的消息 ID
                }));
                // 复制会话设置
                newSession.avatar = currentSession.avatar;
                newSession.context = [...currentSession.context];
                newSession.modelConfig = { ...currentSession.modelConfig };

                // 将新会话添加到列表顶部，并设为当前会话
                set((state) => ({
                    currentSessionIndex: 0,
                    sessions: [newSession, ...state.sessions],
                }));
            },

            /**
             * 清除所有会话，重置为单个空会话
             */
            clearSessions() {
                set(() => ({
                    sessions: [createEmptySession()],
                    currentSessionIndex: 0,
                }));
            },

            /**
             * 选择指定索引的会话作为当前会话
             * @param index 要选择的会话索引
             */
            selectSession(index: number) {
                set({
                    currentSessionIndex: index,
                });
            },

            /**
             * 移动会话在列表中的位置
             * @param from 原始索引
             * @param to 目标索引
             */
            moveSession(from: number, to: number) {
                set((state) => {
                    const { sessions, currentSessionIndex: oldIndex } = state;

                    // 移动会话
                    const newSessions = [...sessions];
                    const session = newSessions[from];
                    newSessions.splice(from, 1); // 从原位置移除
                    newSessions.splice(to, 0, session); // 插入到目标位置

                    // 更新当前会话索引
                    let newIndex = oldIndex === from ? to : oldIndex; // 如果移动的是当前会话，更新索引
                    // 根据移动方向调整其他会话的相对索引
                    if (oldIndex > from && oldIndex <= to) {
                        newIndex -= 1;
                    } else if (oldIndex < from && oldIndex >= to) {
                        newIndex += 1;
                    }

                    return {
                        currentSessionIndex: newIndex,
                        sessions: newSessions,
                    };
                });
            },

            /**
             * 创建一个新的会话
             * @param session 可选的会话部分属性，用于初始化新会话
             */
            newSession(session?: Partial<ChatSession>) {
                const newSession = createEmptySession();

                // 如果提供了 session，则应用配置
                if (session) {
                    const config = useAppConfig.getState();
                    const globalModelConfig = config.modelConfig;

                    // 合并模型配置
                    const modelConfig = {
                        ...globalModelConfig,
                        ...session.modelConfig,
                    };

                    // 合并提供的会话属性
                    Object.assign(newSession, {
                        ...session,
                        modelConfig,
                    });
                }

                // 将新会话添加到列表顶部，并设为当前会话
                set((state) => ({
                    currentSessionIndex: 0,
                    sessions: [newSession, ...state.sessions],
                }));
            },

            /**
             * 切换到下一个或上一个会话
             * @param delta 切换方向，1 为下一个，-1 为上一个
             */
            nextSession(delta: number) {
                const n = get().sessions.length;
                // 循环索引计算
                const limit = (x: number) => (x + n) % n;
                const i = get().currentSessionIndex;
                get().selectSession(limit(i + delta));
            },

            /**
             * 删除指定索引的会话
             * @param index 要删除的会话索引
             */
            deleteSession(index: number) {
                const deletingLastSession = get().sessions.length === 1; // 是否正在删除最后一个会话
                const deletedSession = get().sessions.at(index); // 获取要删除的会话

                if (!deletedSession) return; // 如果会话不存在，则退出

                const sessions = get().sessions.slice(); // 复制会话列表
                sessions.splice(index, 1); // 删除指定索引的会话

                const currentIndex = get().currentSessionIndex;
                // 计算删除后的下一个当前会话索引
                let nextIndex = Math.min(
                    currentIndex - Number(index < currentIndex), // 如果删除的是当前会话之前的会话，索引减 1
                    sessions.length - 1 // 确保索引不越界
                );

                // 如果删除的是最后一个会话，则创建一个新的空会话
                if (deletingLastSession) {
                    nextIndex = 0;
                    sessions.push(createEmptySession());
                }

                // 保存当前状态，用于撤销删除操作
                // const restoreState = {
                //     currentSessionIndex: get().currentSessionIndex,
                //     sessions: get().sessions.slice(),
                // };

                // 更新状态
                set(() => ({
                    currentSessionIndex: nextIndex,
                    sessions,
                }));

                // // 显示提示消息，提供撤销选项
                // showToast(
                //     Locale.Home.DeleteToast,
                //     {
                //         text: Locale.Home.Revert,
                //         onClick() {
                //             // 恢复到删除前的状态
                //             set(() => restoreState);
                //         },
                //     },
                //     5000 // 5 秒后自动消失
                // );
            },

            /**
             * 获取当前选中的会话
             * @returns 当前会话对象，如果索引无效则会自动修正并返回正确的会话
             */
            currentSession() {
                let index = get().currentSessionIndex;
                const sessions = get().sessions;

                // 检查索引是否有效，无效则修正
                if (index < 0 || index >= sessions.length) {
                    index = Math.min(sessions.length - 1, Math.max(0, index));
                    set(() => ({ currentSessionIndex: index })); // 更新修正后的索引
                }

                const session = sessions[index];
                return session;
            },

            /**
             * 当有新消息（通常是机器人的回复）完成时调用
             * @param message 新完成的消息
             * @param targetSession 消息所属的目标会话
             */
            onNewMessage(message: ChatMessage, targetSession: ChatSession) {
                // 更新目标会话状态
                get().updateTargetSession(targetSession, (session) => {
                    // 触发 messages 数组的更新（即使内容不变，也要创建新数组引用以触发 React 更新）
                    session.messages = session.messages.concat();
                    session.lastUpdate = Date.now(); // 更新最后活动时间
                });

                // 更新聊天统计信息
                get().updateStat(message, targetSession);

                // 尝试总结会话（如果需要）
                get().summarizeSession(false, targetSession);
            },

            /**
             * 处理用户输入并发起聊天请求
             * @param content 用户输入的文本内容
             * @param attachImages 可选的附加图片 URL 列表
             */
            async onUserInput(content: string, attachImages?: string[]) {
                const session = get().currentSession();
                const modelConfig = session.modelConfig;

                // 格式化用户输入内容

                let mContent: string | MultimodalContent[] = fillTemplateWith(content, modelConfig);

                // 如果有附加图片且则构建多模态内容数组
                if (attachImages && attachImages.length > 0) {
                    mContent = [
                        ...(content ? [{ type: "text" as const, text: content }] : []), // 添加文本部分（如果存在）
                        // 添加图片部分
                        ...attachImages.map((url) => ({
                            type: "image_url" as const,
                            image_url: { url },
                        })),
                    ];
                }

                // 创建用户消息对象
                const userMessage: ChatMessage = createMessage({
                    role: "user",
                    content: mContent,
                });

                // 创建机器人回复消息对象（初始为空，等待流式更新）
                const botMessage: ChatMessage = createMessage({
                    role: "assistant",
                    streaming: true, // 标记为流式传输中
                    model: modelConfig.model, // 记录使用的模型
                });

                // 获取包含记忆的消息历史记录
                const recentMessages = await get().getMessagesWithMemory();
                // 将用户新消息添加到待发送列表
                const sendMessages = recentMessages.concat(userMessage);
                // 记录新消息在会话中的索引（用于后续可能的错误处理或控制器管理）
                const messageIndex = session.messages.length + 1;

                // 将用户消息和初始机器人消息保存到会话状态
                get().updateTargetSession(session, (session) => {
                    // 确保保存的是处理后的用户消息内容
                    const savedUserMessage = {
                        ...userMessage,
                        content: mContent,
                    };
                    session.messages = session.messages.concat([savedUserMessage, botMessage]);
                });

                // 获取对应的 API 客户端实例
                const api: ClientApi = getClientApi(modelConfig.providerName);
                // 发起聊天请求
                api.llm.chat({
                    messages: sendMessages, // 发送的消息列表
                    config: { ...modelConfig, stream: true }, // 使用当前会话的模型配置，强制开启流式传输
                    // 流式更新回调
                    onUpdate(message) {
                        botMessage.streaming = true; // 保持流式状态
                        if (message) {
                            botMessage.content = message; // 更新机器人消息内容
                        }
                        // 更新会话状态以触发 UI 渲染
                        get().updateTargetSession(session, (session) => {
                            session.messages = session.messages.concat();
                        });
                    },
                    // 完成回调
                    async onFinish(message) {
                        botMessage.streaming = false; // 结束流式状态
                        if (message) {
                            botMessage.content = message; // 设置最终消息内容
                            botMessage.date = new Date().toLocaleString(); // 设置完成时间
                            // 调用 onNewMessage 处理完成的消息（统计、总结等）
                            get().onNewMessage(botMessage, session);
                        }
                        // 从控制器池中移除本次请求的控制器
                        ChatControllerPool.remove(session.id, botMessage.id);
                    },
                    // 工具调用前回调
                    onBeforeTool(tool: ChatMessageTool) {
                        // 将工具调用信息添加到机器人消息中
                        (botMessage.tools = botMessage?.tools || []).push(tool);
                        // 更新会话状态以显示工具调用状态
                        get().updateTargetSession(session, (session) => {
                            session.messages = session.messages.concat();
                        });
                    },
                    // 工具调用后回调
                    onAfterTool(tool: ChatMessageTool) {
                        // 更新机器人消息中对应的工具调用信息（例如，填充工具执行结果）
                        botMessage?.tools?.forEach((t, i, tools) => {
                            if (tool.id == t.id) {
                                tools[i] = { ...tool };
                            }
                        });
                        // 更新会话状态
                        get().updateTargetSession(session, (session) => {
                            session.messages = session.messages.concat();
                        });
                    },
                    // 错误回调
                    onError(error) {
                        const isAborted = error.message?.includes?.("aborted"); // 检查是否是用户主动中止
                        // 在机器人消息中附加错误信息
                        botMessage.content +=
                            "\n\n" +
                            prettyObject({
                                error: true,
                                message: error.message,
                            });
                        botMessage.streaming = false; // 结束流式状态
                        // 如果不是用户中止，则标记用户和机器人消息为错误状态
                        userMessage.isError = !isAborted;
                        botMessage.isError = !isAborted;
                        // 更新会话状态
                        get().updateTargetSession(session, (session) => {
                            session.messages = session.messages.concat();
                        });
                        // 从控制器池中移除控制器
                        ChatControllerPool.remove(session.id, botMessage.id ?? messageIndex);

                        console.error("[Chat] 请求失败 ", error);
                    },
                    // 获取控制器回调
                    onController(controller) {
                        // 将控制器添加到池中，以便可以中止请求
                        ChatControllerPool.addController(session.id, botMessage.id ?? messageIndex, controller);
                    },
                });
            },

            /**
             * 获取当前会话的记忆提示语（如果存在）
             * @returns 格式化为系统消息的记忆提示语，如果不存在则返回 undefined
             */
            getMemoryPrompt() {
                const session = get().currentSession();

                // 如果存在记忆提示语，则格式化为系统消息返回
                if (session.memoryPrompt.length) {
                    return {
                        role: "system",
                        content: Locale.Store.Prompt.History(session.memoryPrompt), // 使用本地化模板包装
                        date: "", // 日期置空
                    } as ChatMessage;
                }
            },

            /**
             * 获取用于发送到 API 的消息列表，包含系统提示、记忆、上下文和近期消息
             * @returns 准备发送的消息列表
             */
            async getMessagesWithMemory() {
                const session = get().currentSession();
                const modelConfig = session.modelConfig;
                // 获取清除上下文的起始索引，默认为 0
                const clearContextIndex = session.clearContextIndex ?? 0;
                const messages = session.messages.slice(); // 复制消息列表
                const totalMessageCount = session.messages.length;

                // 获取预设的上下文提示 (in-context prompts)
                const contextPrompts = session.context.slice();

                // 判断是否需要注入系统提示（接近 OpenAI Web ChatGPT 行为）
                // 条件：配置中启用 && (模型是 gpt-* 或 chatgpt-*)
                const shouldInjectSystemPrompts =
                    modelConfig.enableInjectSystemPrompts &&
                    (session.modelConfig.model.startsWith("gpt-") || session.modelConfig.model.startsWith("chatgpt-"));

                let systemPrompts: ChatMessage[] = []; // 初始化系统提示数组

                // 根据条件构建系统提示
                if (shouldInjectSystemPrompts) {
                    // 注入默认系统模板和
                    systemPrompts = [
                        createMessage({
                            role: "system",
                            content: fillTemplateWith("", {
                                ...modelConfig,
                                template: DEFAULT_SYSTEM_TEMPLATE,
                            }),
                        }),
                    ];
                }

                // 打印全局系统提示（如果存在）
                if (shouldInjectSystemPrompts) {
                    console.log("[Global System Prompt] ", systemPrompts.at(0)?.content ?? "empty");
                }
                // 获取长期记忆提示语
                const memoryPrompt = get().getMemoryPrompt();
                // 判断是否应发送长期记忆
                // 条件：配置中启用 && 存在记忆提示语 && 记忆提示语在清除点之后生成
                const shouldSendLongTermMemory =
                    modelConfig.sendMemory &&
                    session.memoryPrompt &&
                    session.memoryPrompt.length > 0 &&
                    session.lastSummarizeIndex > clearContextIndex;
                // 构建长期记忆提示语数组
                const longTermMemoryPrompts = shouldSendLongTermMemory && memoryPrompt ? [memoryPrompt] : [];
                const longTermMemoryStartIndex = session.lastSummarizeIndex; // 长期记忆的起始消息索引

                // 计算短期记忆的起始消息索引（最近 N 条消息）
                const shortTermMemoryStartIndex = Math.max(0, totalMessageCount - modelConfig.historyMessageCount);

                // 确定实际发送历史消息的起始索引
                // 如果发送长期记忆，则起始点是长期记忆和短期记忆中较早的那个
                // 否则，起始点是短期记忆的起始点
                const memoryStartIndex = shouldSendLongTermMemory
                    ? Math.min(longTermMemoryStartIndex, shortTermMemoryStartIndex)
                    : shortTermMemoryStartIndex;
                // 最终的上下文起始索引，不能早于用户清除点和计算出的记忆起始点
                const contextStartIndex = Math.max(clearContextIndex, memoryStartIndex);
                // 获取模型允许的最大 Token 阈值（用于限制历史消息长度）
                const maxTokenThreshold = modelConfig.max_tokens;

                // 从后向前收集最近的消息，直到达到 Token 阈值
                const reversedRecentMessages = [];
                for (
                    let i = totalMessageCount - 1, tokenCount = 0;
                    i >= contextStartIndex && tokenCount < maxTokenThreshold;
                    i -= 1
                ) {
                    const msg = messages[i];
                    if (!msg || msg.isError) continue; // 跳过空消息或错误消息
                    // 累加 Token 数量
                    tokenCount += estimateTokenLength(getMessageTextContent(msg));
                    // 如果未超出阈值，则添加到列表中
                    if (tokenCount <= maxTokenThreshold) {
                        reversedRecentMessages.push(msg);
                    }
                }

                // 拼接所有部分的消息：系统提示 + 长期记忆 + 上下文提示 + (短期)近期消息
                const recentMessages = [
                    ...systemPrompts,
                    ...longTermMemoryPrompts,
                    ...contextPrompts,
                    ...reversedRecentMessages.reverse(), // 反转近期消息列表，使其按时间顺序排列
                ];

                return recentMessages;
            },

            /**
             * 更新指定会话中指定消息的内容
             * @param sessionIndex 会话索引
             * @param messageIndex 消息索引
             * @param updater 一个函数，接收消息对象并进行修改
             */
            updateMessage(sessionIndex: number, messageIndex: number, updater: (message?: ChatMessage) => void) {
                const sessions = get().sessions;
                const session = sessions.at(sessionIndex);
                const messages = session?.messages;
                // 调用更新器函数修改消息
                updater(messages?.at(messageIndex));
                // 更新状态以触发渲染
                set(() => ({ sessions }));
            },

            /**
             * 重置指定会话，清除消息和记忆提示
             * @param session 要重置的会话对象
             */
            resetSession(session: ChatSession) {
                get().updateTargetSession(session, (session) => {
                    session.messages = [];
                    session.memoryPrompt = "";
                });
            },

            /**
             * 尝试总结会话，生成主题和记忆提示
             * @param refreshTitle 是否强制刷新主题，默认为 false
             * @param targetSession 目标会话对象
             */
            summarizeSession(refreshTitle: boolean = false, targetSession: ChatSession) {
                const config = useAppConfig.getState(); // 获取全局配置
                const session = targetSession;
                const modelConfig = session.modelConfig; // 获取会话的模型配置

                // 如果使用 DALL-E 3 模型，则跳过总结
                if (isDalle3(modelConfig.model)) {
                    return;
                }

                // 确定用于总结的模型和提供商
                // 优先使用会话配置中的 compressModel，否则根据当前模型自动选择
                const [model, providerName] = modelConfig.compressModel
                    ? [modelConfig.compressModel, modelConfig.compressProviderName]
                    : getSummarizeModel(session.modelConfig.model, session.modelConfig.providerName);
                // 获取总结模型的 API 客户端
                const api: ClientApi = getClientApi(providerName as ServiceProvider);

                // 过滤掉错误消息
                const messages = session.messages.filter((msg) => !msg.isError);

                // 判断是否需要生成或刷新主题
                // 条件：(全局启用自动生成标题 && 当前主题是默认主题 && 消息长度超过阈值) 或 强制刷新
                const SUMMARIZE_MIN_LEN = 50; // 触发自动生成标题的最小消息长度（Token 数）
                if (
                    (config.enableAutoGenerateTitle &&
                        session.topic === DEFAULT_TOPIC &&
                        countMessages(messages) >= SUMMARIZE_MIN_LEN) ||
                    refreshTitle
                ) {
                    // 选取最近 N 条消息用于生成主题
                    const startIndex = Math.max(0, messages.length - modelConfig.historyMessageCount);
                    // 准备发送给模型的消息列表（历史消息 + 生成主题的指令）
                    const topicMessages = messages
                        .slice(
                            startIndex < messages.length ? startIndex : messages.length - 1, // 确保至少有一条消息
                            messages.length
                        )
                        .concat(
                            createMessage({
                                role: "user",
                                content: Locale.Store.Prompt.Topic, // 添加生成主题的提示
                            })
                        );
                    // 调用 API 生成主题
                    api.llm.chat({
                        messages: topicMessages,
                        config: {
                            model, // 使用总结模型
                            stream: false, // 非流式获取结果
                            providerName, // 使用总结模型的提供商
                        },
                        onFinish(message, responseRes) {
                            // 如果请求成功
                            if (responseRes?.status === 200) {
                                // 更新会话主题，如果生成的主题为空则使用默认主题
                                get().updateTargetSession(
                                    session,
                                    (session) =>
                                        (session.topic = message.length > 0 ? trimTopic(message) : DEFAULT_TOPIC)
                                );
                            }
                        },
                    });
                }

                // 判断是否需要生成记忆提示 (Memory Prompt)
                // 计算需要总结的消息范围
                const summarizeIndex = Math.max(
                    session.lastSummarizeIndex, // 从上次总结的地方开始
                    session.clearContextIndex ?? 0 // 不能早于清除点
                );
                // 获取待总结的消息列表（过滤掉错误消息）
                let toBeSummarizedMsgs = messages.filter((msg) => !msg.isError).slice(summarizeIndex);

                // 计算待总结消息的总 Token 长度
                const historyMsgLength = countMessages(toBeSummarizedMsgs);

                // 如果待总结消息过长（超过模型最大 Token 或默认值），则截取最近的部分
                if (historyMsgLength > (modelConfig?.max_tokens || 4000)) {
                    const n = toBeSummarizedMsgs.length;
                    toBeSummarizedMsgs = toBeSummarizedMsgs.slice(
                        Math.max(0, n - modelConfig.historyMessageCount) // 保留最近 N 条
                    );
                }

                // 获取当前的记忆提示语（如果有）
                const memoryPrompt = get().getMemoryPrompt();
                if (memoryPrompt) {
                    // 将之前的记忆提示语添加到待总结消息列表的开头，以便进行连续总结
                    toBeSummarizedMsgs.unshift(memoryPrompt);
                }

                // 记录本次总结操作对应的消息索引（用于更新 lastSummarizeIndex）
                const lastSummarizeIndex = session.messages.length;

                console.log(
                    "[Chat History] 准备总结的消息:",
                    toBeSummarizedMsgs,
                    "Token长度:",
                    historyMsgLength,
                    "压缩阈值:",
                    modelConfig.compressMessageLengthThreshold
                );

                // 判断是否需要进行总结（压缩）
                // 条件：待总结消息长度超过阈值 && 配置中启用了发送记忆
                if (historyMsgLength > modelConfig.compressMessageLengthThreshold && modelConfig.sendMemory) {
                    // Fix: 使用 _max_tokens 或 eslint disable 来处理未使用的变量
                    // 从模型配置中解构出 max_tokens，因为总结时不需要限制输出长度（可能？）
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    const { max_tokens, ...modelcfg } = modelConfig;
                    // 调用 API 进行总结
                    api.llm.chat({
                        messages: toBeSummarizedMsgs.concat(
                            createMessage({
                                role: "system",
                                content: Locale.Store.Prompt.Summarize, // 添加总结指令
                                date: "",
                            })
                        ),
                        config: {
                            ...modelcfg, // 使用排除 max_tokens 后的配置
                            stream: true, // 流式获取总结结果（允许逐步显示）
                            model, // 使用总结模型
                            providerName, // 使用总结模型的提供商
                        },
                        // 流式更新回调：逐步更新会话的 memoryPrompt
                        onUpdate(message) {
                            session.memoryPrompt = message;
                        },
                        // 完成回调
                        onFinish(message, responseRes) {
                            // 如果请求成功
                            if (responseRes?.status === 200) {
                                console.log("[Memory] 生成的记忆提示:", message);
                                // 更新会话状态，保存新的记忆提示和最后总结的索引
                                get().updateTargetSession(session, (session) => {
                                    session.lastSummarizeIndex = lastSummarizeIndex;
                                    session.memoryPrompt = message; // 保存最终的记忆提示
                                });
                            }
                        },
                        // 错误回调
                        onError(err) {
                            console.error("[Summarize] 总结失败:", err);
                        },
                    });
                }
            },

            /**
             * 更新指定会话的统计信息（目前只更新字符数）
             * @param message 新增的消息
             * @param session 目标会话
             */
            updateStat(message: ChatMessage, session: ChatSession) {
                get().updateTargetSession(session, (session) => {
                    session.stat.charCount += getMessageTextContent(message).length;
                    // TODO: 实现更准确的词语和 Token 数量统计更新
                });
            },

            /**
             * 更新指定目标会话的状态
             * @param targetSession 要更新的目标会话对象
             * @param updater 一个接收会话对象并进行修改的函数
             */
            updateTargetSession(targetSession: ChatSession, updater: (session: ChatSession) => void) {
                const sessions = get().sessions;
                // 查找目标会话在列表中的索引
                const index = sessions.findIndex((s) => s.id === targetSession.id);
                if (index < 0) return; // 如果未找到，则退出
                // 调用更新器函数修改会话对象
                updater(sessions[index]);
                // 更新 Zustand 状态，传递新的 sessions 数组引用以触发 React 更新
                set(() => ({ sessions: [...sessions] })); // Ensure re-render by creating new array reference
            },

            /**
             * 清除所有应用数据（IndexedDB 和 LocalStorage）并刷新页面
             */
            async clearAllData() {
                await indexedDBStorage.clear(); // 清除 IndexedDB 数据
                localStorage.clear(); // 清除 LocalStorage 数据
                location.reload(); // 刷新页面
            },

            /**
             * 设置最后一次用户输入的内容（用于输入框记忆）
             * @param lastInput 最后一次输入的内容
             */
            setLastInput(lastInput: string) {
                set({
                    lastInput,
                });
            },
        };

        return methods;
    },
    {
        /** 持久化存储配置 */
        name: StoreKey.Chat, // LocalStorage 中的 key
        version: 3.3, // 当前状态版本号
        /**
         * 状态迁移函数，用于处理旧版本状态到新版本的转换
         * @param persistedState 从存储中加载的旧状态
         * @param version 旧状态的版本号
         * @returns 迁移后的新状态
         */
        migrate(persistedState, version) {
            const state = persistedState as any;
            // 深拷贝旧状态作为新状态的基础
            const newState = JSON.parse(JSON.stringify(state)) as typeof DEFAULT_CHAT_STATE;

            // 版本 < 2 的迁移：旧版 session 结构转换
            if (version < 2) {
                newState.sessions = []; // 初始化为空数组
                const oldSessions = state.sessions;
                for (const oldSession of oldSessions) {
                    const newSession = createEmptySession();
                    newSession.topic = oldSession.topic;
                    newSession.messages = [...oldSession.messages];
                    // 为旧会话添加默认的记忆相关配置
                    newSession.modelConfig.sendMemory = true;
                    newSession.modelConfig.historyMessageCount = 4;
                    newSession.modelConfig.compressMessageLengthThreshold = 1000;
                    newState.sessions.push(newSession);
                }
            }

            // 版本 < 3 的迁移：为会话和消息添加 nanoid
            if (version < 3) {
                newState.sessions.forEach((s) => {
                    s.id = nanoid(); // 为会话生成 ID
                    s.messages.forEach((m) => (m.id = nanoid())); // 为消息生成 ID
                });
            }

            // 版本 < 3.1 的迁移：为旧会话启用 enableInjectSystemPrompts
            // 解决旧会话无法自动开启注入系统提示的问题
            if (version < 3.1) {
                newState.sessions.forEach((s) => {
                    // Fix: 使用更安全的 hasOwnProperty 检查方式
                    // 检查会话是否已显式设置该属性，如果没有，则应用当前全局配置
                    if (!Object.prototype.hasOwnProperty.call(s.modelConfig, "enableInjectSystemPrompts")) {
                        // 使用当前的全局配置值，而不是默认值，因为用户可能已经更改了全局设置
                        const config = useAppConfig.getState();
                        s.modelConfig.enableInjectSystemPrompts = config.modelConfig.enableInjectSystemPrompts;
                    }
                });
            }

            // 版本 < 3.2 的迁移：为每个会话添加默认的总结模型配置
            if (version < 3.2) {
                newState.sessions.forEach((s) => {
                    const config = useAppConfig.getState();
                    s.modelConfig.compressModel = config.modelConfig.compressModel;
                    s.modelConfig.compressProviderName = config.modelConfig.compressProviderName;
                });
            }

            // 版本 < 3.3 的迁移：恢复每个会话的总结模型配置为空（让 getSummarizeModel 动态选择）
            if (version < 3.3) {
                newState.sessions.forEach((s) => {
                    // const config = useAppConfig.getState();
                    s.modelConfig.compressModel = "";
                    s.modelConfig.compressProviderName = "";
                });
            }

            return newState as any; // 返回迁移后的状态
        },
    }
);
