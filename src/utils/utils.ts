import { useEffect, useState } from "react";
import { showToast } from "../app/ui-lib";
import Locale from "../locales";
import { RequestMessage } from "../client/api";
import { REQUEST_TIMEOUT_MS, REQUEST_TIMEOUT_MS_FOR_THINKING, ServiceProvider } from "../constant";
// import { fetch as tauriFetch, ResponseType } from "@tauri-apps/api/http";
import { fetch as tauriStreamFetch } from "./stream";
import { VISION_MODEL_REGEXES, EXCLUDE_VISION_MODEL_REGEXES } from "../constant";
import { useAccessStore } from "../store";
import { ModelSize } from "../types/typing";
import { message } from "antd";

/**
 * 修剪主题字符串，移除末尾的标点符号和首尾的引号。
 * @param topic - 需要修剪的主题字符串。
 * @returns 修剪后的主题字符串。
 */
export function trimTopic(topic: string) {
    // Fix an issue where double quotes still show in the Indonesian language
    // This will remove the specified punctuation from the end of the string
    // and also trim quotes from both the start and end if they exist.
    return (
        topic
            // fix for gemini
            .replace(/^["""*]+|["""*]+$/g, "")
            .replace(/[，。！？""""、,.!?*]*$/, "")
    );
}

/**
 * 将文本复制到剪贴板。优先使用 Tauri API（如果可用），否则回退到浏览器 navigator.clipboard API 或 document.execCommand。
 * @param text - 需要复制的文本。
 */
export async function copyToClipboard(text: string) {
    try {
        if (window.__TAURI__) {
            window.__TAURI__.writeText(text);
        } else {
            await navigator.clipboard.writeText(text);
        }

        message.success(Locale.Copy.Success);
    } catch (error) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand("copy");
            message.success(Locale.Copy.Success);
        } catch (error) {
            message.error(Locale.Copy.Failed);
        }
        document.body.removeChild(textArea);
    }
}

/**
 * 将文本内容下载为文件。优先使用 Tauri API（如果可用），否则创建一个链接并模拟点击下载。
 * @param text - 要下载的文本内容。
 * @param filename - 下载的文件名。
 */
export async function downloadAs(text: string, filename: string) {
    if (window.__TAURI__) {
        const result = await window.__TAURI__.dialog.save({
            defaultPath: `${filename}`,
            filters: [
                {
                    name: `${filename.split(".").pop()} files`,
                    extensions: [`${filename.split(".").pop()}`],
                },
                {
                    name: "All Files",
                    extensions: ["*"],
                },
            ],
        });

        if (result !== null) {
            try {
                await window.__TAURI__.fs.writeTextFile(result, text);
                showToast(Locale.Download.Success);
            } catch (error) {
                showToast(Locale.Download.Failed);
            }
        } else {
            showToast(Locale.Download.Failed);
        }
    } else {
        const element = document.createElement("a");
        element.setAttribute("href", "data:text/plain;charset=utf-8," + encodeURIComponent(text));
        element.setAttribute("download", filename);

        element.style.display = "none";
        document.body.appendChild(element);

        element.click();

        document.body.removeChild(element);
    }
}

/**
 * 读取用户选择的文件内容。创建一个隐藏的文件输入元素并模拟点击。
 * @returns 返回一个 Promise，解析为文件内容的字符串。
 */
export function readFromFile() {
    return new Promise<string>((res, rej) => {
        const fileInput = document.createElement("input");
        fileInput.type = "file";
        fileInput.accept = "application/json";

        fileInput.onchange = (event: any) => {
            const file = event.target.files[0];
            const fileReader = new FileReader();
            fileReader.onload = (e: any) => {
                res(e.target.result);
            };
            fileReader.onerror = (e) => rej(e);
            fileReader.readAsText(file);
        };

        fileInput.click();
    });
}

/**
 * 判断当前设备是否为 iOS。
 * @returns 如果是 iOS 设备则返回 true，否则返回 false。
 */
export function isIOS() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /iphone|ipad|ipod/.test(userAgent);
}

/**
 * 获取并监听窗口大小变化的自定义 Hook。
 * @returns 返回包含当前窗口宽度和高度的对象。
 */
export function useWindowSize() {
    const [size, setSize] = useState({
        width: window.innerWidth,
        height: window.innerHeight,
    });

    useEffect(() => {
        const onResize = () => {
            setSize({
                width: window.innerWidth,
                height: window.innerHeight,
            });
        };

        window.addEventListener("resize", onResize);

        return () => {
            window.removeEventListener("resize", onResize);
        };
    }, []);

    return size;
}

export const MOBILE_MAX_WIDTH = 600;
/**
 * 判断当前屏幕是否为移动端尺寸（宽度小于等于 MOBILE_MAX_WIDTH）。
 * @returns 如果是移动端屏幕尺寸则返回 true，否则返回 false。
 */
export function useMobileScreen() {
    const { width } = useWindowSize();

    return width <= MOBILE_MAX_WIDTH;
}

/**
 * 判断当前浏览器是否为 Firefox。
 * @returns 如果是 Firefox 浏览器则返回 true，否则返回 false。
 */
export function isFirefox() {
    return typeof navigator !== "undefined" && /firefox/i.test(navigator.userAgent);
}

/**
 * 选择 DOM 元素内容或复制指定文本。如果当前有选区，则不执行复制操作。
 * @param el - 目标 HTML 元素。
 * @param content - 要复制的文本内容。
 * @returns 如果执行了复制操作则返回 true，否则返回 false。
 */
export function selectOrCopy(el: HTMLElement, content: string) {
    const currentSelection = window.getSelection();

    if (currentSelection?.type === "Range") {
        return false;
    }

    copyToClipboard(content);

    return true;
}

/**
 * 获取 DOM 元素的实际内容宽度（减去左右内边距）。
 * @param dom - 目标 HTML 元素。
 * @returns 元素的实际内容宽度。
 */
function getDomContentWidth(dom: HTMLElement) {
    const style = window.getComputedStyle(dom);
    const paddingWidth = parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);
    const width = dom.clientWidth - paddingWidth;
    return width;
}

/**
 * 获取或创建一个用于测量的隐藏 DOM 元素。
 * @param id - 测量元素的 ID。
 * @param init - 可选的初始化函数，在创建新元素时调用。
 * @returns 测量 DOM 元素。
 */
function getOrCreateMeasureDom(id: string, init?: (dom: HTMLElement) => void) {
    let dom = document.getElementById(id);

    if (!dom) {
        dom = document.createElement("span");
        dom.style.position = "absolute";
        dom.style.wordBreak = "break-word";
        dom.style.fontSize = "14px";
        dom.style.transform = "translateY(-200vh)";
        dom.style.pointerEvents = "none";
        dom.style.opacity = "0";
        dom.id = id;
        document.body.appendChild(dom);
        init?.(dom);
    }

    return dom!;
}

/**
 * 根据内容自动计算 TextArea 元素所需的行数。
 * @param dom - 目标 TextArea 元素。
 * @returns 计算出的行数。
 */
export function autoGrowTextArea(dom: HTMLTextAreaElement) {
    const measureDom = getOrCreateMeasureDom("__measure");
    const singleLineDom = getOrCreateMeasureDom("__single_measure", (dom) => {
        dom.innerText = "TEXT_FOR_MEASURE";
    });

    const width = getDomContentWidth(dom);
    measureDom.style.width = width + "px";
    measureDom.innerText = dom.value !== "" ? dom.value : "1";
    measureDom.style.fontSize = dom.style.fontSize;
    measureDom.style.fontFamily = dom.style.fontFamily;
    const endWithEmptyLine = dom.value.endsWith("\n");
    const height = parseFloat(window.getComputedStyle(measureDom).height);
    const singleLineHeight = parseFloat(window.getComputedStyle(singleLineDom).height);

    const rows = Math.round(height / singleLineHeight) + (endWithEmptyLine ? 1 : 0);

    return rows;
}

/**
 * 获取 CSS 变量的值。
 * @param varName - CSS 变量的名称（例如 `--primary-color`）。
 * @returns CSS 变量的值（去除首尾空格）。
 */
export function getCSSVar(varName: string) {
    return getComputedStyle(document.body).getPropertyValue(varName).trim();
}

/**
 * 检测当前操作系统是否为 macOS 或 iOS 设备。
 * @returns 如果是 macOS 或 iOS 则返回 true，否则返回 false。
 */
export function isMacOS(): boolean {
    if (typeof window !== "undefined") {
        const userAgent = window.navigator.userAgent.toLocaleLowerCase();
        const macintosh = /iphone|ipad|ipod|macintosh/.test(userAgent);
        return !!macintosh;
    }
    return false;
}

/**
 * 获取消息对象的纯文本内容。
 * @param message - 请求消息对象。
 * @returns 消息的纯文本内容。如果内容是数组，则返回第一个文本类型的内容。
 */
export function getMessageTextContent(message: RequestMessage) {
    if (typeof message.content === "string") {
        return message.content;
    }
    for (const c of message.content) {
        if (c.type === "text") {
            return c.text ?? "";
        }
    }
    return "";
}

/**
 * 获取消息对象的纯文本内容，并过滤掉表示"思考中"的行（以 "> " 开头）。
 * @param message - 请求消息对象。
 * @returns 过滤后的纯文本内容。
 */
export function getMessageTextContentWithoutThinking(message: RequestMessage) {
    let content = "";

    if (typeof message.content === "string") {
        content = message.content;
    } else {
        for (const c of message.content) {
            if (c.type === "text") {
                content = c.text ?? "";
                break;
            }
        }
    }

    // Filter out thinking lines (starting with "> ")
    return content
        .split("\n")
        .filter((line) => !line.startsWith("> ") && line.trim() !== "")
        .join("\n")
        .trim();
}

/**
 * 从消息对象中提取所有图片 URL。
 * @param message - 请求消息对象。
 * @returns 包含图片 URL 的字符串数组。
 */
export function getMessageImages(message: RequestMessage): string[] {
    if (typeof message.content === "string") {
        return [];
    }
    const urls: string[] = [];
    for (const c of message.content) {
        if (c.type === "image_url") {
            urls.push(c.image_url?.url ?? "");
        }
    }
    return urls;
}

/**
 * 判断给定的模型名称是否为视觉模型。
 * 会检查环境变量配置和预定义的正则表达式列表。
 * @param model - 模型名称字符串。
 * @returns 如果是视觉模型则返回 true，否则返回 false。
 */
export function isVisionModel(model: string) {
    const visionModels = useAccessStore.getState().visionModels;
    const envVisionModels = visionModels?.split(",").map((m) => m.trim());
    if (envVisionModels?.includes(model)) {
        return true;
    }
    return (
        !EXCLUDE_VISION_MODEL_REGEXES.some((regex) => regex.test(model)) &&
        VISION_MODEL_REGEXES.some((regex) => regex.test(model))
    );
}

/**
 * 判断给定的模型名称是否为 Dall-E 3。
 * @param model - 模型名称字符串。
 * @returns 如果是 Dall-E 3 模型则返回 true，否则返回 false。
 */
export function isDalle3(model: string) {
    return "dall-e-3" === model;
}

/**
 * 根据模型名称获取请求超时时间。
 * 特定模型（如 Dall-E, o1, o3, deepseek-r, -thinking 后缀）使用较长的超时时间。
 * @param model - 模型名称字符串。
 * @returns 请求超时时间（毫秒）。
 */
export function getTimeoutMSByModel(model: string) {
    model = model.toLowerCase();
    if (
        model.startsWith("dall-e") ||
        model.startsWith("dalle") ||
        model.startsWith("o1") ||
        model.startsWith("o3") ||
        model.includes("deepseek-r") ||
        model.includes("-thinking")
    )
        return REQUEST_TIMEOUT_MS_FOR_THINKING;
    return REQUEST_TIMEOUT_MS;
}

/**
 * 获取指定模型支持的图片尺寸。
 * @param model - 模型名称字符串。
 * @returns 支持的图片尺寸字符串数组 (例如 "1024x1024")。如果模型不支持或未指定，则返回空数组。
 */
export function getModelSizes(model: string): ModelSize[] {
    if (isDalle3(model)) {
        return ["1024x1024", "1792x1024", "1024x1792"];
    }
    if (model.toLowerCase().includes("cogview")) {
        return ["1024x1024", "768x1344", "864x1152", "1344x768", "1152x864", "1440x720", "720x1440"];
    }
    return [];
}

/**
 * 判断指定模型是否支持自定义图片尺寸。
 * @param model - 模型名称字符串。
 * @returns 如果模型支持自定义尺寸则返回 true，否则返回 false。
 */
export function supportsCustomSize(model: string): boolean {
    return getModelSizes(model).length > 0;
}

/**
 * 根据服务提供商和模型名称决定是否显示插件功能。
 * @param provider - 服务提供商类型。
 * @param model - 模型名称。
 * @returns 如果应显示插件则返回 true，否则返回 false。
 */
export function showPlugins(provider: ServiceProvider, model: string) {
    if (provider == ServiceProvider.DeepSeek) {
        return true;
    }
    return false;
}

/**
 * 封装的 fetch 函数，优先使用 Tauri 的 fetch API（如果可用），否则使用浏览器的原生 fetch。
 * @param url - 请求的 URL。
 * @param options - fetch 请求的配置选项。
 * @returns 返回一个 Promise，解析为 fetch 的响应。
 */
export function fetch(url: string, options?: Record<string, unknown>): Promise<any> {
    if (window.__TAURI__) {
        return tauriStreamFetch(url, options);
    }
    return window.fetch(url, options);
}

/**
 * 用于 HTTP 请求库（如 axios）的适配器，使用上面封装的 `fetch` 函数。
 * @param config - 请求配置对象，包含 baseURL, url, params, data (body) 等。
 * @returns 返回一个 Promise，解析为包含 status, statusText, headers, data 的响应对象。
 */
export function adapter(config: Record<string, unknown>) {
    const { baseURL, url, params, data: body, ...rest } = config;
    const path = baseURL ? `${baseURL}${url}` : url;
    const fetchUrl = params ? `${path}?${new URLSearchParams(params as any).toString()}` : path;
    return fetch(fetchUrl as string, { ...rest, body }).then((res) => {
        const { status, headers, statusText } = res;
        return res.text().then((data: string) => ({ status, statusText, headers, data }));
    });
}

/**
 * 安全地访问 localStorage，在 localStorage 不可用时提供回退并打印警告。
 * @returns 返回一个包含 getItem, setItem, removeItem, clear 方法的对象，行为类似 localStorage。
 */
export function safeLocalStorage(): {
    getItem: (key: string) => string | null;
    setItem: (key: string, value: string) => void;
    removeItem: (key: string) => void;
    clear: () => void;
} {
    let storage: Storage | null;

    try {
        if (typeof window !== "undefined" && window.localStorage) {
            storage = window.localStorage;
        } else {
            storage = null;
        }
    } catch (e) {
        console.error("localStorage is not available:", e);
        storage = null;
    }

    return {
        getItem(key: string): string | null {
            if (storage) {
                return storage.getItem(key);
            } else {
                console.warn(`Attempted to get item "${key}" from localStorage, but localStorage is not available.`);
                return null;
            }
        },
        setItem(key: string, value: string): void {
            if (storage) {
                storage.setItem(key, value);
            } else {
                console.warn(`Attempted to set item "${key}" in localStorage, but localStorage is not available.`);
            }
        },
        removeItem(key: string): void {
            if (storage) {
                storage.removeItem(key);
            } else {
                console.warn(`Attempted to remove item "${key}" from localStorage, but localStorage is not available.`);
            }
        },
        clear(): void {
            if (storage) {
                storage.clear();
            } else {
                console.warn("Attempted to clear localStorage, but localStorage is not available.");
            }
        },
    };
}

/**
 * 根据 OpenAPI 操作对象生成操作 ID。
 * 如果 operation.operationId 存在，则直接使用；否则根据 HTTP 方法和路径生成。
 * @param operation - 包含 operationId?, method, path 的对象。
 * @returns 生成的操作 ID 字符串。
 */
export function getOperationId(operation: { operationId?: string; method: string; path: string }) {
    // pattern '^[a-zA-Z0-9_-]+$'
    return operation?.operationId || `${operation.method.toUpperCase()}${operation.path.replaceAll("/", "_")}`;
}

/**
 * 检查并安装客户端更新（如果可用）。仅在 Tauri 环境下有效。
 * @returns 返回一个 Promise，在更新检查和安装完成后解析。
 */
export function clientUpdate() {
    // this a wild for updating client app
    return window.__TAURI__?.updater
        .checkUpdate()
        .then((updateResult) => {
            if (updateResult.shouldUpdate) {
                window.__TAURI__?.updater
                    .installUpdate()
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    .then((_result) => {
                        // 使用 _result 表示有意未使用
                        showToast(Locale.Settings.Update.Success);
                    })
                    .catch((e) => {
                        console.error("[Install Update Error]", e);
                        showToast(Locale.Settings.Update.Failed);
                    });
            }
        })
        .catch((e) => {
            console.error("[Check Update Error]", e);
            showToast(Locale.Settings.Update.Failed);
        });
}

// https://gist.github.com/iwill/a83038623ba4fef6abb9efca87ae9ccb
/**
 * 比较两个语义化版本号字符串。
 * @param a - 第一个版本号字符串。
 * @param b - 第二个版本号字符串。
 * @returns 如果 a < b 返回 -1，如果 a > b 返回 1，如果 a == b 返回 0。
 */
export function semverCompare(a: string, b: string) {
    if (a.startsWith(b + "-")) return -1;
    if (b.startsWith(a + "-")) return 1;
    return a.localeCompare(b, undefined, {
        numeric: true,
        sensitivity: "case",
        caseFirst: "upper",
    });
}

/**
 * 根据ID获取树形数据中的最下层节点
 * @param tree 树形数据
 * @param targetId 要查找的目标ID
 * @param childrenKey 子节点的键名，默认为'children'
 * @returns 找到的最下层节点或null
 */
export function getDeepestNodeById(
    tree: any[],
    targetId: string | number,
    idKey: string = "id",
    childrenKey: string = "children"
) {
    // 辅助函数：查找节点及其最深子节点
    function findNodeWithDepth(nodes: any[], id: string | number, depth: number = 0): { node: any; depth: number } {
        let result: { node: any; depth: number } = { node: null, depth: -1 };

        for (const node of nodes) {
            // 如果找到匹配ID的节点
            if (node[idKey] === id) {
                // 检查该节点是否有子节点
                if (node[childrenKey] && node[childrenKey].length > 0) {
                    // 递归查找所有子树，找出最深的路径
                    let deepestChild = { node: null, depth: -1 };
                    for (const child of node[childrenKey]) {
                        const childResult = findNodeWithDepth([child], child[idKey], depth + 1);
                        if (childResult.depth > deepestChild.depth) {
                            deepestChild = childResult;
                        }
                    }
                    return deepestChild;
                } else {
                    // 如果没有子节点，返回当前节点
                    return { node, depth };
                }
            }

            // 如果当前节点有子节点，递归查找
            if (node[childrenKey] && node[childrenKey].length > 0) {
                const childResult = findNodeWithDepth(node[childrenKey], id, depth + 1);
                if (childResult.node && childResult.depth > result.depth) {
                    result = childResult;
                }
            }
        }

        return result;
    }

    const result = findNodeWithDepth(tree, targetId);
    return result.node;
}

/**
 * 根据 ID 获取节点及其所有父节点路径
 * @param tree 树形结构数组
 * @param targetId 目标节点 ID
 * @param idKey ID 属性名，默认为 "id"
 * @param childrenKey 子节点数组属性名，默认为 "children"
 * @returns 包含目标节点及其所有父节点的数组，按从根到叶的顺序排列
 */
export function getNodeWithAncestors(
    tree: any[],
    targetId: string | number,
    idKey: string = "id",
    childrenKey: string = "children"
): any[] {
    // 辅助函数：深度优先搜索查找节点路径
    function findNodePath(nodes: any[], id: string | number, path: any[] = []): any[] | null {
        // 遍历当前层级的节点
        for (const node of nodes) {
            // 创建当前路径
            const currentPath = [...path, node];

            // 找到目标节点，返回完整路径
            if (node[idKey] === id) {
                return currentPath;
            }

            // 如果有子节点，递归查找
            if (node[childrenKey] && node[childrenKey].length > 0) {
                const foundPath = findNodePath(node[childrenKey], id, currentPath);
                // 如果在子节点中找到路径，则返回
                if (foundPath) {
                    return foundPath;
                }
            }
        }

        // 未找到路径返回 null
        return null;
    }

    // 执行查找并返回结果
    const result = findNodePath(tree, targetId);
    return result || [];
}

/**
 *
 * @param html html格式字符串
 * @returns 自动转为字符串文本，携带换行与缩进
 */
export function formatHtmlWithLists(html: string): string {
    // 如果输入为空，返回空字符串
    if (!html) return "";

    // 创建解析器和解析文档
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    // 创建一个结果字符串，用于存储格式化后的文本
    let result = "";

    // 递归处理 DOM 节点
    function processNode(node: Node, indentLevel: number = 0): void {
        // 为不同节点类型应用不同的处理
        switch (node.nodeType) {
            case Node.TEXT_NODE: {
                // 处理文本节点：添加到结果中（如果不是空白）
                const text = node.textContent?.trim() || "";
                if (text) {
                    result += text + " ";
                }
                break;
            }

            case Node.ELEMENT_NODE: {
                const element = node as Element;
                const tagName = element.tagName.toLowerCase();

                // 根据标签类型处理
                switch (tagName) {
                    case "ol": {
                        // 处理有序列表
                        result += "\n"; // 在列表前添加换行

                        // 获取起始编号（如果指定）
                        let startNum = 1;
                        if (element.hasAttribute("start")) {
                            startNum = parseInt(element.getAttribute("start") || "1", 10);
                        }

                        // 处理每个列表项
                        const listItems = element.querySelectorAll("li");
                        listItems.forEach((item, index) => {
                            const itemNum = startNum + index;

                            // 添加适当的缩进和序号
                            result += "\n" + "\t".repeat(indentLevel * 2) + `${itemNum}. `;

                            // 递归处理列表项的子节点
                            Array.from(item.childNodes).forEach((child) => {
                                processNode(child, indentLevel + 1);
                            });

                            // 每个列表项后面添加换行
                            // if (!result.endsWith("\n")) {
                            //     result += "\n";
                            // }
                        });

                        result += "\n";
                        break;
                    }

                    case "ul": {
                        // 处理无序列表
                        result += "\n";

                        // 处理每个列表项
                        element.querySelectorAll("li").forEach((item) => {
                            // 添加适当的缩进和项目符号
                            result += "\n" + "\t".repeat(indentLevel * 2) + "• ";

                            // 递归处理列表项的子节点
                            Array.from(item.childNodes).forEach((child) => {
                                processNode(child, indentLevel + 1);
                            });

                            // 每个列表项后面添加换行
                            // if (!result.endsWith("\n")) {
                            //     result += "\n";
                            // }
                        });

                        result += "\n";
                        break;
                    }

                    case "p":
                    case "div":
                    case "h1":
                    case "h2":
                    case "h3":
                    case "h4":
                    case "h5":
                    case "h6": {
                        // 段落、div 和标题前后添加换行
                        result += "\n";

                        // // 递归处理子节点
                        Array.from(element.childNodes).forEach((child) => {
                            processNode(child, indentLevel);
                        });

                        // if (!result.endsWith("\n")) {
                        //     result += "\n";
                        // }
                        break;
                    }

                    case "br": {
                        // 处理换行
                        result += "\n" + " ".repeat(indentLevel * 2);
                        break;
                    }

                    default: {
                        // 默认情况：递归处理子节点
                        Array.from(element.childNodes).forEach((child) => {
                            processNode(child, indentLevel);
                        });
                        break;
                    }
                }
                break;
            }
        }
    }

    // 开始处理 body 元素的所有子节点
    Array.from(doc.body.childNodes).forEach((node) => {
        processNode(node, 1);
    });

    // 清理文本：删除多余的空白和换行
    return result
        .replace(/\n{3,}/g, "\n\n") // 将多个连续换行替换为两个换行
        .replace(/[ \t]+\n/g, "\n") // 删除行尾空白
        .trim(); // 删除字符串开头和结尾的空白
}

/**
 * 查找表格所在的对话框元素
 * @param element 开始查找的元素
 * @returns 对话框元素或null
 */
function findDialogParent(element: HTMLElement | null) {
    if (!element) return null;

    // 向上查找具有对话框特征的父元素
    let current: HTMLElement | null = element;
    while (current) {
        if (current.classList.contains("ant-modal-content")) {
            return current;
        }
        current = current.parentElement;
    }

    return null;
}
/**
 *
 * @param extraHeight 额外的高度
 * @param id 当前页面中有多个table时需要制定table的id
 * @returns 获取第一个表格的可视化高度
 */
export function getTableScroll(params?: { extraHeight?: number; id: string }) {
    let extraHeight = 0;

    if (typeof params?.extraHeight == "undefined") {
        extraHeight = 54;
    } else {
        extraHeight = params?.extraHeight;
    }

    let tHeader = null;
    let dialogElement = null;
    if (params?.id) {
        tHeader = document.getElementById(params.id)
            ? document?.getElementById(params.id)?.getElementsByClassName("ant-table-thead")?.[0]
            : null;

        // 寻找对话框元素（通常是表格的某个父元素）
        dialogElement = findDialogParent(tHeader as HTMLElement | null);
    } else {
        tHeader = document.getElementsByClassName("ant-table-thead")[0];
    }
    //表格内容距离顶部的距离
    const tHeaderBottom = Math.floor(tHeader?.getBoundingClientRect()?.bottom ?? 0);
    if (tHeader && dialogElement) {
        console.log(
            dialogElement,
            dialogElement.getBoundingClientRect()?.top,
            tHeader?.getBoundingClientRect()?.bottom,
            dialogElement.getBoundingClientRect()?.bottom - tHeader?.getBoundingClientRect()?.bottom - extraHeight
        );
        return Math.floor(
            document.body.clientHeight - tHeaderBottom - dialogElement.getBoundingClientRect()?.top - extraHeight
        );
    }
    if (tHeader) {
        const height = document.body.clientHeight - tHeaderBottom - extraHeight;
        return height;
    }

    //窗体高度-表格内容顶部的高度-表格内容底部的高度
}

/**
 *
 * @param path 路径字符串
 * @param level 获取第几层级的数据，默认从右向左，0为最右边
 * @param splitChar 分割符，默认为/
 * @returns 返回对应路径下的值
 */
export function getPathValue(path: string, level: number = 0, splitChar: string = "/") {
    if (path) {
        const paths = path.split(splitChar);
        return paths[paths.length - 1 - level];
    }
    return "";
}

/**
 *
 * @param treeData 树形数据
 * @param childrenKey 子数据key
 * @returns 转换树形数据为数组数据
 */
export function treeToArray(treeData: any[], childrenKey: string = "children"): any[] {
    return treeData.reduce((acc, node) => {
        const { ...rest } = node;
        const children = node[childrenKey];
        return [...acc, rest, ...(children ? treeToArray(children) : [])];
    }, []);
}

/**
 *
 * @param treeData 数组数据
 * @param parentKey 父级key
 * @returns 数组转树形数据
 */
export function arrayToTree(treeData: any[], key: string = "id", parentKey: string = "parentId") {
    const map = new Map<string, any>();
    const result: any[] = [];

    // 第一次遍历：将每个节点添加到 Map 中
    treeData.forEach((item) => {
        // 初始化子节点数组
        map.set(item[key], { ...item, children: [] });
    });

    // 第二次遍历：构建树结构
    treeData.forEach((item) => {
        const node = map.get(item[key]);
        if (!node) return;

        const isParentNode = map.get(item[parentKey]);
        if (!isParentNode) {
            result.push(node);
        } else {
            // 子节点处理：找到父节点并添加到其 children 中
            const parent = map.get(item[parentKey]);
            if (parent) {
                parent.children?.push(node);
            }
        }
    });

    return result;
}

/**
 *
 * @param birthDate 出生日期
 * @returns 年龄
 */
export function calculateAge(birthDate: Date | string): number {
    // 如果传入的是字符串，将其转换为Date对象
    const birth = typeof birthDate === "string" ? new Date(birthDate) : birthDate;

    // 获取当前日期
    const today = new Date();

    // 计算基础年龄差
    let age = today.getFullYear() - birth.getFullYear();

    // 检查月份和日期，如果生日还没到，则年龄减1
    const birthMonth = birth.getMonth();
    const todayMonth = today.getMonth();

    if (todayMonth < birthMonth || (todayMonth === birthMonth && today.getDate() < birth.getDate())) {
        age--;
    }

    // 确保年龄不为负数
    return Math.max(0, age);
}

/**
 * 格式化时间
 * @param time 时间（时间戳、日期字符串或Date对象）
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 *
 * 支持的格式化占位符：
 * - YYYY: 四位年份
 * - YY: 两位年份
 * - MM: 两位月份（01-12）
 * - M: 一位月份（1-12）
 * - DD: 两位日期（01-31）
 * - D: 一位日期（1-31）
 * - HH: 两位小时，24小时制（00-23）
 * - H: 一位小时，24小时制（0-23）
 * - hh: 两位小时，12小时制（01-12）
 * - h: 一位小时，12小时制（1-12）
 * - mm: 两位分钟（00-59）
 * - m: 一位分钟（0-59）
 * - ss: 两位秒（00-59）
 * - s: 一位秒（0-59）
 * - SSS: 三位毫秒（000-999）
 * - A: AM/PM
 * - a: am/pm
 * - Q: 季度（1-4）
 * - dddd: 星期几的完整名称
 * - ddd: 星期几的缩写
 * - d: 一周中的第几天（0-6，0代表星期日）
 */
export function formatTime(time: number | string | Date, format: string = "YYYY-MM-DD"): string {
    // 如果没有传入时间，则返回空字符串
    if (time === null || time === undefined || time === "") {
        return "";
    }

    let date: Date;

    // 根据不同类型的时间参数，生成Date对象
    if (typeof time === "number") {
        // 如果时间戳为13位（毫秒），则直接使用，如果是10位（秒），则转换为毫秒
        date = new Date(time < 10000000000 ? time * 1000 : time);
    } else if (typeof time === "string") {
        // 处理可能的格式，如 '2023-01-01' 或 '2023/01/01'
        // 注：如果字符串格式不规范，可能导致解析错误或时区问题
        date = new Date(time);
    } else {
        date = time;
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
        console.warn(`Invalid date: ${time}`);
        return "";
    }

    const year = date.getFullYear();
    const month = date.getMonth() + 1; // getMonth() 返回 0-11
    const day = date.getDate();
    const hours24 = date.getHours();
    const hours12 = hours24 % 12 || 12; // 12小时制
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    const milliseconds = date.getMilliseconds();
    const dayOfWeek = date.getDay(); // 0-6，0代表星期日

    // 星期几的名称
    const weekdaysFull = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    const weekdaysShort = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

    // 计算季度
    const quarter = Math.floor((month - 1) / 3) + 1;

    // 替换格式字符串中的占位符
    return format.replace(/Y{4}|Y{2}|M{1,2}|D{1,2}|H{1,2}|h{1,2}|m{1,2}|s{1,2}|S{3}|A|a|Q|dddd|ddd|d/g, (match) => {
        switch (match) {
            case "YYYY":
                return `${year}`;
            case "YY":
                return `${year}`.slice(-2);
            case "MM":
                return month < 10 ? `0${month}` : `${month}`;
            case "M":
                return `${month}`;
            case "DD":
                return day < 10 ? `0${day}` : `${day}`;
            case "D":
                return `${day}`;
            case "HH":
                return hours24 < 10 ? `0${hours24}` : `${hours24}`;
            case "H":
                return `${hours24}`;
            case "hh":
                return hours12 < 10 ? `0${hours12}` : `${hours12}`;
            case "h":
                return `${hours12}`;
            case "mm":
                return minutes < 10 ? `0${minutes}` : `${minutes}`;
            case "m":
                return `${minutes}`;
            case "ss":
                return seconds < 10 ? `0${seconds}` : `${seconds}`;
            case "s":
                return `${seconds}`;
            case "SSS":
                return milliseconds < 10
                    ? `00${milliseconds}`
                    : milliseconds < 100
                        ? `0${milliseconds}`
                        : `${milliseconds}`;
            case "A":
                return hours24 < 12 ? "AM" : "PM";
            case "a":
                return hours24 < 12 ? "am" : "pm";
            case "Q":
                return `${quarter}`;
            case "dddd":
                return weekdaysFull[dayOfWeek];
            case "ddd":
                return weekdaysShort[dayOfWeek];
            case "d":
                return `${dayOfWeek}`;
            default:
                return match;
        }
    });
}
