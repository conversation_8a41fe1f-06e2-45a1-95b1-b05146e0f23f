import { Button, Flex } from "antd";
import styles from "./index.module.scss";
import PreviewFile from "@/PreviewFile";
import { PreviewFileExpose } from "@/typing";
import { useRef } from "react";
import messageService from "@/lib/message";

const PreviewOrDownload = ({
    fileBlob,
    fileName,
    showPreview,
    showDownload,
}: {
    fileBlob?: Blob;
    fileName: string;
    showPreview: boolean;
    showDownload: boolean;
}) => {
    const previewFileRef = useRef<PreviewFileExpose>(null);

    const previewFile = () => {
        previewFileRef.current?.showDialog(fileName, fileBlob);
    };

    const downloadFile = () => {
        if (!fileBlob) {
            messageService.error("当前文件内容为空，无法下载");
            return;
        }

        const url = window.URL.createObjectURL(fileBlob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Flex justify="space-between" align="center" className={styles["preview-or-download-wrapper"]}>
            {fileName}
            <div className="">
                {showPreview && (
                    <Button type="link" onClick={previewFile}>
                        预览
                    </Button>
                )}
                {showDownload && (
                    <Button type="link" onClick={downloadFile}>
                        下载
                    </Button>
                )}
            </div>
            <PreviewFile ref={previewFileRef} />
        </Flex>
    );
};

export default PreviewOrDownload;
