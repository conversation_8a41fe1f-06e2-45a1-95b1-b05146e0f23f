import { EChartsOption } from "echarts/types/dist/echarts";
import Tags from "@/Tags";
import styles from "./index.module.scss";
import { Flex, Rate } from "antd";
import EChart from "@/components/Echarts";
import AiReasonHintIcon from "@/icons/talentTool/ai-reason-hint.svg";
import { AIAnalysisResp, CandidateTagsResp } from "@/store/modules/candidate";
import { useState } from "react";

// 是否符合要求
// const StatusTags = ({ status }: { status: string }) => {
//     const tags: { [key in string]: { label: string; backgroundColor: string } } = {
//         1: {
//             label: "符合要求",
//             backgroundColor: "#13BF47",
//         },
//         2: {
//             label: "不符合要求",
//             backgroundColor: "#FF4D4D26",
//         },
//     };

//     return (
//         <Tag
//             icon={<CheckCircleOutlined />}
//             style={{ backgroundColor: tags[status].backgroundColor, color: "#fff" }}
//             bordered={false}
//         >
//             {tags[status].label}
//         </Tag>
//     );
// };

const TagsRadar = ({ detail }: { detail: AIAnalysisResp }) => {
    const option: EChartsOption = {
        color: ["#43ADFF"],
        grid: {
            left: "12px",
            right: "12px",
            top: "12px",
            bottom: "12px",
        },
        radar: {
            indicator: [{ name: "硬性门槛" }, { name: "能力适配" }, { name: "潜力预测" }],
            shape: "circle",
            radius: "70%",
            axisLine: {
                lineStyle: {
                    color: "#d8d8d8",
                },
            },
            splitNumber: 2,
            axisLabel: {
                color: "",
            },
            splitArea: {
                show: false,
            },
            splitLine: {
                lineStyle: {
                    color: "#d8d8d8",
                },
            },
            axisName: {
                color: "var(--sub-text-color)",
            },
        },
        series: [
            {
                type: "radar",
                data: [
                    {
                        value: [
                            detail.abilityLayerScore.hardRequirementsScore,
                            detail.abilityLayerScore.capabilityFitScore,
                            detail.abilityLayerScore.potentialForecastScore,
                        ],
                        areaStyle: {
                            opacity: 0.2,
                        },
                        symbol: "none",
                    },
                ],
            },
        ],
    };

    return (
        <div style={{ height: "200px", width: "100%" }}>
            <EChart option={option} />
        </div>
    );
};

// const PersonJobFitRadar = () => {
//     const option: EChartsOption = {
//         color: ["#43ADFF", "#87C728"],
//         grid: {
//             left: "12px",
//             right: "12px",
//             top: "30px",
//             bottom: "12px",
//         },
//         legend: {
//             data: ["应聘人才", "应聘岗位"],
//             itemHeight: 10,
//             itemWidth: 10,
//             textStyle: {
//                 color: "#000000db",
//             },
//             bottom: "0",
//         },
//         radar: {
//             indicator: [{ name: "技术方向" }, { name: "发展潜力" }, { name: "个人特质" }],
//             shape: "circle",
//             radius: "70%",
//             axisLine: {
//                 lineStyle: {
//                     color: "#d8d8d8",
//                 },
//             },
//             splitNumber: 2,
//             axisLabel: {
//                 color: "",
//             },
//             splitArea: {
//                 show: false,
//             },
//             splitLine: {
//                 lineStyle: {
//                     color: "#d8d8d8",
//                 },
//             },
//             axisName: {
//                 color: "var(--sub-text-color)",
//             },
//         },
//         series: [
//             {
//                 type: "radar",
//                 data: [
//                     {
//                         value: [4200, 3000, 20000, 35000, 50000, 18000],
//                         symbol: "none",
//                         name: "应聘人才",
//                         areaStyle: {
//                             opacity: 0.2,
//                         },
//                     },
//                     {
//                         value: [400, 6000, 16000, 35000, 50000, 18000],
//                         symbol: "none",
//                         name: "应聘岗位",
//                         areaStyle: {
//                             opacity: 0.2,
//                         },
//                     },
//                 ],
//             },
//         ],
//     };

//     return (
//         <div style={{ height: "200px", width: "100%" }}>
//             <EChart option={option} />
//         </div>
//     );
// };

const ProfileFit = ({ detail, aiAnalysisStr }: { detail: AIAnalysisResp; aiAnalysisStr: string }) => {
    const [tags, setTags] = useState<{ id: number; label: string; score: number; list: CandidateTagsResp[] }[]>([
        {
            id: 1,
            label: "硬性门槛",
            score: detail.abilityLayerScore.hardRequirementsScore,
            list: detail.abilityLayerScore.hardRequirementsTags ?? [],
        },
        {
            id: 2,
            label: "能力适配",
            score: detail.abilityLayerScore.capabilityFitScore,
            list: detail.abilityLayerScore.capabilityFitTags ?? [],
        },
        {
            id: 3,
            label: "潜力预测",
            score: detail.abilityLayerScore.potentialForecastScore,
            list: detail.abilityLayerScore.potentialForecastTags ?? [],
        },
    ]);

    return (
        <div className={styles["profile-fit-wrapper"]}>
            <div className={styles["profile-fit__top"]}>
                <div className={styles["profile-fit__top-score-wrapper"]}>
                    {/* <Tag style={{ color: "#0099F2FF", backgroundColor: "#0099F226" }} bordered={false}>
                        {detail.scoreText}
                    </Tag> */}
                    <Rate value={detail.matchScore} disabled allowHalf></Rate>
                    <div>
                        <span className={styles["profile-fit__top-score"]}>{detail.matchScore}</span>分
                    </div>
                </div>
                <div className={styles["profile-fit__top-item"]}>
                    <div className={styles["profile-fit__top-item-label"]}>人才预估</div>
                    <div className={styles["profile-fit__top-item-value"]}>{detail.talentEstimate}</div>
                </div>
                <div className={styles["profile-fit__top-item"]}>
                    <div className={styles["profile-fit__top-item-label"]}>项目经验</div>
                    <div className={styles["profile-fit__top-item-value"]}>
                        <span>{detail.projectExperienceCount ?? 0}</span>
                        <span className={styles["profile-fit__top-item-unit"]}>个</span>
                    </div>
                </div>
                <div className={styles["profile-fit__top-item"]}>
                    <div className={styles["profile-fit__top-item-label"]}>综合排名</div>
                    <div className={styles["profile-fit__top-item-value"]}>
                        <span>{detail.ranking}</span>
                        {/* <Tag
                            style={{ color: "#0099F2FF", backgroundColor: "#0099F226", marginLeft: 4, marginRight: 0 }}
                            bordered={false}
                        >
                            aaaa
                        </Tag> */}
                    </div>
                </div>
            </div>
            <div className={styles["profile-fit__middle"]}>
                <div className={styles["profile-fit__middle__left"]}>
                    {tags.map((item: any) => {
                        return (
                            <Flex key={item.id} style={{ width: "100%" }}>
                                <div className={styles["profile-fit__middle-label"]}>{item.label}</div>
                                <div className={styles["profile-fit__middle-value"]}>
                                    <Rate value={item.score} disabled allowHalf></Rate>
                                    <div>
                                        <Tags
                                            tags={
                                                item?.list?.map((item2: any) => {
                                                    return {
                                                        name: item2.name,
                                                        isMatched: item2.isMatched,
                                                    };
                                                }) ?? []
                                            }
                                            color="#465BE8"
                                        ></Tags>
                                    </div>
                                </div>
                            </Flex>
                        );
                    })}
                </div>
                <div className={styles["profile-fit__middle__right"]}>
                    <TagsRadar detail={detail} />
                </div>
            </div>
            <div className={styles["profile-fit__label"]}>人岗匹配度</div>
            <div className={styles["profile-fit__bottom"]}>
                <div className={styles["profile-fit__bottom__left"]}>
                    <Flex align="center">
                        <Flex align="center" gap={4} className={styles["AI-label"]}>
                            <AiReasonHintIcon />
                            AI评估
                        </Flex>
                        {/* <StatusTags status={"1"} /> */}
                    </Flex>
                    <div className={styles["profile-fit__bottom__left__content"]}>{aiAnalysisStr}</div>
                </div>
                {/* <div className={styles["profile-fit__bottom__right"]}>
                    <PersonJobFitRadar />
                </div> */}
            </div>
        </div>
    );
};

export default ProfileFit;
