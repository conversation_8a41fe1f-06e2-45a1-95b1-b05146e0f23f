import { forwardRef, useImperativeHandle, useState } from "react";
import ChatDialog from "@/ChatDialog";
import { PreviewFileExpose } from "@/typing";
import RenderFile from "@/RenderFile";
import messageService from "@/lib/message";

const PreviewFile = forwardRef<PreviewFileExpose>((props, ref) => {
    useImperativeHandle(ref, () => ({
        showDialog: (fileName: string, blob?: Blob) => {
            if (!blob) {
                messageService.error("当前文件内容为空，无法预览");
                return;
            }
            setResumeBlob(blob);
            setShowPreview(true);
            setFileName(fileName);
        },
    }));

    const [resumeBlob, setResumeBlob] = useState<Blob>();
    const [showPreview, setShowPreview] = useState<boolean>(false);
    const [fileName, setFileName] = useState<string>("");

    return (
        <ChatDialog
            width="60%"
            open={showPreview}
            title="预览文件"
            confirmLoading={false}
            onCancel={() => setShowPreview(false)}
            footer={null}
        >
            <RenderFile blob={resumeBlob} fileName={fileName} showDownload={false} />
        </ChatDialog>
    );
});
PreviewFile.displayName = "PreviewFile";

export default PreviewFile;
