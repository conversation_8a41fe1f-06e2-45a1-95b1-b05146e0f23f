// 原是auth.tsx也就是涉及登录密码和token密钥的部分，我给拆成一个公共组件了 内容是一个顶部的宣传横幅，对界面会自动处理成自适应
// TODO：后续记得把文字内容也处理成props
import { useState, useEffect } from "react";
import clsx from "clsx";
import Delete from "@/icons/close.svg";
import Arrow from "@/icons/arrow.svg";
import Logo from "@/icons/logo.svg";
import { useMobileScreen } from "@/utils";
import { safeLocalStorage } from "@/utils";
import { SAAS_CHAT_URL } from "@/constant";
import { trackAuthorizationPageBannerToCPaymentClick } from "@/utils/auth-settings-events";
import Locale from "@/locales";
import styles from "./styles.module.scss";

const storage = safeLocalStorage();

interface TopBannerProps {
    className?: string;
    onClose?: () => void;
    showCloseButton?: boolean;
    presistKey?: string;
}

export function TopBanner({
    className,
    onClose,
    showCloseButton = true,
    presistKey = "bannerDismissed",
}: TopBannerProps) {
    const [isHovered, setIsHovered] = useState(false);
    const [isVisible, setIsVisible] = useState(true);
    const isMobile = useMobileScreen();

    useEffect(() => {
        const bannerDismissed = storage.getItem(presistKey);
        if (!bannerDismissed) {
            storage.setItem(presistKey, "false");
            setIsVisible(true);
        } else if (bannerDismissed === "true") {
            setIsVisible(false);
        }
    }, [presistKey]);

    const handleMouseEnter = () => setIsHovered(true);
    const handleMouseLeave = () => setIsHovered(false);

    const handleClose = () => {
        setIsVisible(false);
        storage.setItem(presistKey, "true");
        onClose?.();
    };

    if (!isVisible) {
        return null;
    }

    return (
        <div
            className={clsx(styles["top-banner"], className)}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
        >
            <div className={clsx(styles["top-banner-inner"], "no-dark")}>
                <Logo className={styles["top-banner-logo"]} />
                <span>
                    {Locale.Auth.TopTips}
                    <a
                        href={SAAS_CHAT_URL}
                        rel="stylesheet"
                        onClick={() => {
                            trackAuthorizationPageBannerToCPaymentClick();
                        }}
                    >
                        {Locale.Settings.Access.SaasStart.ChatNow}
                        <Arrow style={{ marginLeft: "4px" }}></Arrow>
                    </a>
                </span>
            </div>
            {showCloseButton && (isHovered || isMobile) && (
                <Delete classname={styles["top-banner-close"]} onClick={handleClose}></Delete>
            )}
        </div>
    );
}
