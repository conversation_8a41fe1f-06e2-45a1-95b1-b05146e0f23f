"use client";
// 引入需要的React库和钩子函数
import React, { Fragment, useEffect, useRef, useState } from "react";
import { Avatar, Button, Dropdown, Tooltip } from "antd";
import Icon from "@ant-design/icons";
// 引入样式文件
import styles from "./index.module.scss";
// 引入共享组件和图标
import { IconButton } from "@/components/Button";
import Locale from "@/locales";
import SidebarLogoIcon from "@/icons/sidebar/sidebar-logo.svg";
import ResizeIcon from "@/icons/sidebar/resize.svg";
import NewChatIcon from "@/icons/sidebar/new-chat.svg";
import TalentIcon from "@/icons/sidebar/talent.svg";
import WorkSpaceIcon from "@/icons/sidebar/work-space.svg";
import AccountIcon from "@/icons/sidebar/account.svg";
import RecordIcon from "@/icons/sidebar/record.svg";
import CandidateIcon from "@/icons/Candidate/CandidateIcon.svg";
import InterviewDashboardIcon from "@/icons/sidebar/interview-dashboard.svg";
import EliminationPoolIcon from "@/icons/sidebar/elimination-pool.svg";
// 引入应用状态管理
import { useAppConfig, useChatStore } from "@/store";
import { useTitleStore } from "@/store/modules/chat-content/title";

// 定义侧边栏宽度的常量
import {
    DEFAULT_SIDEBAR_WIDTH, // 默认宽度
    MAX_SIDEBAR_WIDTH, // 最大宽度
    MIN_SIDEBAR_WIDTH, // 最小宽度
    NARROW_SIDEBAR_WIDTH, // 窄模式宽度
    getResponsiveSidebarWidth, // 响应式侧边栏宽度
    Path, // 应用路径
} from "@/constant";

// 引入路由导航功能
import { usePathname, useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import clsx from "clsx";
import useUserInfoStore from "@/store/userInfo";
import { CaretDownOutlined, PushpinOutlined } from "@ant-design/icons";
import { encryptAdvanced } from "@/utils/aes";
import { logoutApi } from "@/request/modules/common";
import messageService from "@/lib/message";
import { checkRolePermissionApi } from "@/request/modules/dashboard";
import { RespParams } from "@/types/typing";

// 定义超窄侧边栏宽度
const SUPER_NARROW_WIDTH = 100;
// 定义侧边栏绝对最小宽度
const MIN_ALLOWED_WIDTH = 50;

// 动态导入聊天列表组件
const ChatList = dynamic(async () => (await import("@/app/ChatList/chat-list")).ChatList, {
    loading: () => null, // 加载时不显示任何内容
});

// 键盘快捷键功能
export function useHotKey() {
    const chatStore = useChatStore();

    useEffect(() => {
        // 当按下键盘按键时触发的函数
        const onKeyDown = (e: KeyboardEvent) => {
            if (e.altKey || e.ctrlKey) {
                if (e.key === "ArrowUp") {
                    // Alt+上箭头切换到上一个聊天
                    chatStore.nextSession(-1);
                } else if (e.key === "ArrowDown") {
                    // Alt+下箭头切换到下一个聊天
                    chatStore.nextSession(1);
                }
            }
        };

        // 添加键盘事件监听
        window.addEventListener("keydown", onKeyDown);
        // 组件卸载时移除事件监听
        return () => window.removeEventListener("keydown", onKeyDown);
    });
}

// 侧边栏拖拽调整宽度功能
export function useDragSideBar() {
    // 限制侧边栏最大宽度的函数
    const limit = (x: number) => Math.min(MAX_SIDEBAR_WIDTH, x);
    // 限制侧边栏最小宽度的函数
    const limitMin = (x: number) => Math.max(MIN_ALLOWED_WIDTH, x);

    const config = useAppConfig();
    const startX = useRef(0); // 记录开始拖拽时的X坐标
    const startDragWidth = useRef(config.sidebarWidth ?? getResponsiveSidebarWidth()); // 开始拖拽时的宽度
    // const lastUpdateTime = useRef(Date.now()); // 上次更新时间
    // const [isCollapsed, setIsCollapsed] = useState(false); // 侧边栏是否折叠
    const { sideBarisCollapsed } = config;

    // 初始化侧边栏宽度
    useEffect(() => {
        // 如果侧边栏宽度未设置或等于默认固定值，则使用响应式宽度
        if (!config.sidebarWidth || config.sidebarWidth === DEFAULT_SIDEBAR_WIDTH) {
            const responsiveWidth = getResponsiveSidebarWidth();
            config.update((config) => {
                config.sidebarWidth = responsiveWidth;
                config.prevSidebarWidth = responsiveWidth;
            });
        }
    }, [config]);

    // 监听窗口大小变化
    useEffect(() => {
        let resizeTimeout: NodeJS.Timeout;

        const handleResize = () => {
            // 防抖处理，避免频繁更新
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                // 只有在非折叠状态下才考虑调整宽度
                if (!sideBarisCollapsed) {
                    const responsiveWidth = getResponsiveSidebarWidth();
                    // 检查用户是否手动调整过宽度
                    // 只有当宽度接近默认值或尚未设置时，才自动调整
                    const currentWidth = config.sidebarWidth;
                    const isDefault =
                        !currentWidth ||
                        currentWidth === DEFAULT_SIDEBAR_WIDTH ||
                        (config.prevSidebarWidth && Math.abs(currentWidth - config.prevSidebarWidth) < 5);

                    if (isDefault) {
                        config.update((config) => {
                            config.sidebarWidth = responsiveWidth;
                            config.prevSidebarWidth = responsiveWidth;
                        });
                    }
                }
            }, 600);
        };

        window.addEventListener("resize", handleResize);
        return () => {
            clearTimeout(resizeTimeout);
            window.removeEventListener("resize", handleResize);
        };
    }, [config, sideBarisCollapsed]);

    // 切换侧边栏宽窄模式
    const toggleSideBar = () => {
        config.update((config) => {
            if (config.sidebarWidth < MIN_SIDEBAR_WIDTH) {
                config.sidebarWidth = getResponsiveSidebarWidth(); // 使用响应式宽度
            } else {
                config.sidebarWidth = NARROW_SIDEBAR_WIDTH; // 否则切换到窄模式
            }
        });
    };

    // 收起或展开侧边栏
    const toggleCollapse = () => {
        config.update((config) => {
            config.sideBarisCollapsed = !sideBarisCollapsed;
            if (!sideBarisCollapsed) {
                // 正在折叠：保存当前宽度，以便展开时恢复。
                // config.sidebarWidth 保留其当前值，--sidebar-width 将通过 useEffect 因 isCollapsed 变化而变为 0px。
                // 确保 prevSidebarWidth 保存的是一个有效的、可恢复的宽度。
                if (config.sidebarWidth && config.sidebarWidth > MIN_ALLOWED_WIDTH) {
                    config.prevSidebarWidth = config.sidebarWidth;
                } else {
                    // 如果当前宽度已经是 NARROW 或更小，或者无效，
                    // 确保 prevSidebarWidth 有一个合理的默认值以便展开。
                    // 如果 config.prevSidebarWidth 已经有一个有效值，则保留它。
                    if (!config.prevSidebarWidth || config.prevSidebarWidth <= MIN_ALLOWED_WIDTH) {
                        config.prevSidebarWidth = getResponsiveSidebarWidth();
                    }
                }
                // 不需要在此处修改 config.sidebarWidth 为 0 或特定折叠宽度，
                // 因为 isCollapsed 状态会驱动 useEffect 将 --sidebar-width 设置为 0px。
            } else {
                // 正在展开：从 prevSidebarWidth 恢复宽度，如果没有则使用响应式宽度。
                config.sidebarWidth =
                    config.prevSidebarWidth && config.prevSidebarWidth > MIN_ALLOWED_WIDTH
                        ? config.prevSidebarWidth
                        : getResponsiveSidebarWidth();
            }
        });
    };

    // 开始拖拽时的处理函数
    const onDragStart = (e: MouseEvent) => {
        // 记住初始宽度和鼠标位置
        startX.current = e.clientX;
        startDragWidth.current = config.sidebarWidth;
        const dragStartTime = Date.now();

        let animationFrameId: number | null = null;

        // 拖拽过程中的处理函数
        const handleDragMove = (e: MouseEvent) => {
            if (animationFrameId !== null) {
                cancelAnimationFrame(animationFrameId);
            }

            animationFrameId = requestAnimationFrame(() => {
                // 计算拖拽的距离
                const width = e.clientX - startX.current;
                // 限制最小宽度不小于MIN_ALLOWED_WIDTH
                const nextWidth = limit(limitMin(startDragWidth.current + width));
                // 更新侧边栏宽度
                config.update((config) => {
                    /*
                    if (nextWidth < MIN_SIDEBAR_WIDTH && nextWidth > MIN_ALLOWED_WIDTH) {
                        config.sidebarWidth = NARROW_SIDEBAR_WIDTH; // 太窄时切换到窄模式
                    } else {
                        config.sidebarWidth = nextWidth;
                    }
                    */
                    // 新增逻辑：处理吸附点
                    let targetWidth;
                    if (nextWidth <= MIN_ALLOWED_WIDTH) {
                        targetWidth = MIN_ALLOWED_WIDTH;
                    } else if (nextWidth < SUPER_NARROW_WIDTH) {
                        // 小于 100 但大于 50，吸附到 50
                        targetWidth = MIN_ALLOWED_WIDTH;
                    } else {
                        // 大于等于 260，使用实际宽度
                        targetWidth = nextWidth;
                    }
                    config.sidebarWidth = targetWidth;
                });

                // 如果拖拽宽度大于0，确保侧边栏展开状态
                if (nextWidth > 0 && sideBarisCollapsed) {
                    config.update((config) => {
                        config.sideBarisCollapsed = false;
                    });
                }
            });
        };

        // 结束拖拽时的处理函数
        const handleDragEnd = () => {
            // 移除事件监听
            window.removeEventListener("mousemove", handleDragMove);
            window.removeEventListener("mouseup", handleDragEnd);

            // 如果用户点击拖拽图标但没有实际拖拽，则切换侧边栏模式
            const shouldFireClick = Date.now() - dragStartTime < 300;
            if (shouldFireClick) {
                toggleSideBar();
            }

            // 取消动画帧
            if (animationFrameId !== null) {
                cancelAnimationFrame(animationFrameId);
            }
        };

        // 添加拖拽相关的事件监听
        window.addEventListener("mousemove", handleDragMove);
        window.addEventListener("mouseup", handleDragEnd);
    };

    const shouldNarrow = config.sidebarWidth < MIN_SIDEBAR_WIDTH && config.sidebarWidth > 0; // 是否应该显示窄侧边栏
    const shouldSuperNarrow = config.sidebarWidth < SUPER_NARROW_WIDTH && config.sidebarWidth > 0; // 是否应该显示超窄侧边栏

    // 根据侧边栏宽度设置CSS变量
    useEffect(() => {
        let sideBarWidth;
        if (sideBarisCollapsed) {
            sideBarWidth = "0px";
        } else {
            const barWidth = shouldNarrow
                ? NARROW_SIDEBAR_WIDTH // 窄模式宽度
                : limit(config.sidebarWidth ?? getResponsiveSidebarWidth()); // 使用响应式宽度
            sideBarWidth = `${barWidth}px`; // 移动设备时占满屏幕
        }
        document.documentElement.style.setProperty("--sidebar-width", sideBarWidth);
    }, [config.sidebarWidth, shouldNarrow, sideBarisCollapsed]);

    const expandSideBarToDefault = () => {
        config.update((c) => {
            const targetWidth =
                c.prevSidebarWidth && c.prevSidebarWidth > NARROW_SIDEBAR_WIDTH
                    ? c.prevSidebarWidth
                    : getResponsiveSidebarWidth();
            c.sidebarWidth = targetWidth;
            c.sideBarisCollapsed = false; // 确保侧边栏不是折叠状态
        });
    };

    // 固定展开侧边栏（从超窄模式永久展开）
    const pinSideBarExpanded = () => {
        config.update((c) => {
            const targetWidth =
                c.prevSidebarWidth && c.prevSidebarWidth > SUPER_NARROW_WIDTH
                    ? c.prevSidebarWidth
                    : getResponsiveSidebarWidth();
            c.sidebarWidth = targetWidth;
            c.sideBarisCollapsed = false; // 确保侧边栏不是折叠状态
        });
    };

    return {
        onDragStart, // 返回拖拽开始处理函数
        shouldNarrow, // 返回是否应该使用窄侧边栏
        shouldSuperNarrow, // 返回是否应该使用超窄侧边栏
        isCollapsed: sideBarisCollapsed, // 返回侧边栏是否折叠
        toggleCollapse, // 返回折叠/展开侧边栏的函数
        expandSideBarToDefault, // <--- 返回展开侧边栏到默认宽度的函数
        pinSideBarExpanded, // <--- 返回hover态固定展开侧边栏的函数
    };
}

// 侧边栏容器组件
export function SideBarContainer(props: {
    children: React.ReactNode; // 子元素
    onDragStart: (e: MouseEvent) => void; // 开始拖拽的处理函数
    shouldNarrow: boolean; // 是否显示窄侧边栏
    shouldSuperNarrow: boolean; // 是否显示超窄侧边栏
    isCollapsed: boolean; // 是否折叠侧边栏
    className?: string; // 可选的CSS类名
    onHoverChange?: (isHovered: boolean) => void; // 悬停状态变化回调
}) {
    const { children, className, onDragStart, shouldNarrow, shouldSuperNarrow, isCollapsed, onHoverChange } = props;
    const [isHovered, setIsHovered] = useState(false);

    // 处理鼠标进入事件
    const handleMouseEnter = () => {
        if (shouldSuperNarrow && !isCollapsed) {
            setIsHovered(true);
            onHoverChange?.(true);
        }
    };

    // 处理鼠标离开事件
    const handleMouseLeave = () => {
        if (shouldSuperNarrow && !isCollapsed) {
            setIsHovered(false);
            onHoverChange?.(false);
        }
    };

    return (
        <div className={styles["sidebar-wrapper"]}>
            <div
                className={clsx(styles.sidebar, className, {
                    [styles["narrow-sidebar"]]: shouldNarrow, // 如果是窄模式，添加对应的CSS类
                    [styles["super-narrow-sidebar"]]: shouldSuperNarrow, // 如果是超窄模式，添加对应的CSS类
                    [styles["super-narrow-sidebar-hovered"]]: shouldSuperNarrow && isHovered, // 超窄模式悬停状态
                    [styles["collapsed-sidebar"]]: isCollapsed, // 如果是折叠模式，添加对应的CSS类
                })}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {children}
                {/* 侧边栏右侧的拖拽条 */}
                <div className={styles["sidebar-drag"]} onMouseDown={(e) => onDragStart(e as any)}></div>
            </div>
        </div>
    );
}

// 侧边栏头部组件
export function SideBarHeader(props: {
    children?: React.ReactNode; // 子元素
    shouldNarrow?: boolean; // 是否显示窄侧边栏
    shouldSuperNarrow?: boolean; // 是否显示超窄侧边栏
    isCollapsed?: boolean; // 是否折叠侧边栏
    toggleCollapse?: () => void; // 折叠/展开侧边栏的函数
    isHovered?: boolean; // 是否处于悬停状态
    pinSideBarExpanded?: () => void; // 固定展开侧边栏的函数
}) {
    const { children, shouldNarrow, shouldSuperNarrow, isCollapsed, toggleCollapse, isHovered, pinSideBarExpanded } =
        props;

    return (
        <Fragment>
            <div
                className={clsx(styles["sidebar-header"], {
                    [styles["sidebar-header-narrow"]]: shouldNarrow, // 窄模式下的头部样式
                    [styles["sidebar-header-super-narrow"]]: shouldSuperNarrow && !isHovered, // 超窄模式下的头部样式，悬停时不应用
                    [styles["sidebar-header-collapsed"]]: isCollapsed, // 折叠模式下的头部样式
                })}
            >
                {!isCollapsed && <Icon className={styles["sidebar-logo"]} component={() => <SidebarLogoIcon />} />}
                {!(shouldSuperNarrow && !isHovered) && (
                    <>
                        {/* 如果是超窄模式且悬停状态，显示固定展开按钮 */}
                        {shouldSuperNarrow && isHovered ? (
                            <Tooltip
                                title={"固定展开侧边栏"}
                                placement="right"
                                styles={{
                                    root: { fontSize: "12px" },
                                }}
                            >
                                <Button
                                    icon={<PushpinOutlined />}
                                    type="text"
                                    onClick={pinSideBarExpanded}
                                    className={clsx({
                                        [styles["collapse-button"]]: true,
                                        [styles["collapse-button-collapsed"]]: isCollapsed,
                                    })}
                                ></Button>
                            </Tooltip>
                        ) : (
                            /* 否则显示正常的收起按钮 */
                            <Tooltip
                                title={"收起侧栏"}
                                placement="right"
                                styles={{
                                    root: { fontSize: "12px" },
                                }}
                            >
                                <Button
                                    icon={<ResizeIcon />}
                                    type="text"
                                    onClick={toggleCollapse}
                                    className={clsx({
                                        [styles["collapse-button"]]: true,
                                        [styles["collapse-button-collapsed"]]: isCollapsed,
                                    })}
                                ></Button>
                            </Tooltip>
                        )}
                    </>
                )}
            </div>
            {!isCollapsed && children}
        </Fragment>
    );
}

// 侧边栏主体组件
export function SideBarBody(props: {
    children: React.ReactNode; // 子元素
    onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void; // 点击事件处理函数
    shouldShowExpandButton?: boolean; //是否显示展开按钮
    onExpand?: () => void; // 展开按钮的点击处理函数
}) {
    const { onClick, children, shouldShowExpandButton, onExpand } = props;
    return (
        <div
            className={clsx(styles["sidebar-body"], {
                [styles["siderbar-body-narrow"]]: shouldShowExpandButton,
            })}
            onClick={onClick}
        >
            {children}
            {shouldShowExpandButton && (
                <div className={styles["sidebar-body-expand-button-container"]}>
                    <Tooltip
                        title={"展开侧栏"}
                        placement="top"
                        styles={{ root: { fontSize: "12px" } }}
                        getPopupContainer={() => document.body}
                    >
                        <Button
                            icon={<ResizeIcon />}
                            type="text"
                            onClick={onExpand}
                            className={styles["sidebar-body-expand-button"]}
                        />
                    </Tooltip>
                </div>
            )}
        </div>
    );
}

// 侧边栏底部组件
export function SideBarTail(props: {
    avatar: string;
    name: string;
    shouldSuperNarrow?: boolean;
    isCollapsed?: boolean;
    isHovered?: boolean;
}) {
    const { shouldSuperNarrow, isCollapsed, isHovered } = props;

    const { token, user, state, setToken, setState, setUser } = useUserInfoStore((state) => ({
        token: state.token,
        user: state.user,
        state: state.state,
        setToken: state.setToken,
        setState: state.setState,
        setUser: state.setUser,
    }));

    const onClick = ({ key }: { key: string }) => {
        if (key === "logout") {
            setToken("");
            setState("");
            setUser({
                avatar: "",
                emp_id: "",
                emp_name: "",
                gender: "",
                perms: [],
                permGroups: [],
            });
            logoutApi();

            if (state === "cas") window.open(`${process.env.NEXT_PUBLIC_CAS_URL}logout`, "_blank");
            return;
        }
        if (key === "toAdmin") {
            // 临时添加可访问权限
            const allowEmpIDList = ["10012607", "10017296", "10005435", "10065971", "10004286", "10007883", "10003068"];

            if (!allowEmpIDList.includes(user.emp_id)) {
                messageService.error("暂无权限访问");
                return;
            }

            const obj = { token, user };
            const data = encryptAdvanced(JSON.stringify(obj));
            const urlParams = encodeURIComponent(data);

            window.open(`${process.env.NEXT_PUBLIC_ADMIN_URL}?user=${urlParams}`, "_blank");
            return;
        }
    };

    return (
        <div
            className={clsx(styles["sidebar-bottom"], {
                [styles["sidebar-bottom-narrow"]]: !(shouldSuperNarrow && !isHovered) && !isCollapsed,
                [styles["sidebar-bottom-super-narrow"]]: shouldSuperNarrow && !isHovered && !isCollapsed,
            })}
        >
            {
                <Dropdown
                    getPopupContainer={() => document.body}
                    menu={{
                        items: [
                            { key: "logout", label: "退出登录" },
                            { key: "toAdmin", label: "后台管理" },
                        ],
                        onClick,
                    }}
                >
                    <div className={clsx("flex-row align-center", styles["user-dropdown-container"])}>
                        {!isCollapsed && <Avatar src={user.avatar} icon={<AccountIcon />} alt="用户头像"></Avatar>}
                        {!isCollapsed && !(shouldSuperNarrow && !isHovered) && (
                            <>
                                <div className={styles["sidebar-username"]}>
                                    {/* <Tooltip title={`${user.emp_name}(${user.emp_id})`} placement="right"> */}
                                    <span className={styles["username-text"]}>
                                        {user.emp_name}({user.emp_id})
                                    </span>
                                    {/* </Tooltip> */}
                                </div>
                                <CaretDownOutlined className={styles["dropdown-icon"]} />
                            </>
                        )}
                    </div>
                </Dropdown>
            }
        </div>
    );
}

// 完整的侧边栏组件
export function SideBar(props: { className?: string }) {
    useHotKey(); // 使用键盘快捷键
    const {
        onDragStart,
        shouldNarrow,
        shouldSuperNarrow,
        isCollapsed,
        toggleCollapse,
        expandSideBarToDefault, // 获取新函数
        pinSideBarExpanded, // 获取固定展开函数
    } = useDragSideBar(); // 使用侧边栏拖拽功能
    const router = useRouter();
    const pathname = usePathname();
    const chatStore = useChatStore(); // 获取聊天状态管理器
    // 获取当前路由
    const addTitleComponent = useTitleStore((state) => state.addTitleComponent);
    const removeTitleComponent = useTitleStore((state) => state.removeTitleComponent);
    const { update } = useAppConfig();
    // 管理悬停状态
    const [isHovered, setIsHovered] = useState(false);

    // 校验是否允许展示候选人界面
    const [showCandidate, setShowCandidate] = useState(false);

    useEffect(() => {
        const checkCandidatePermission = async () => {
            const res: RespParams<boolean> = await (await checkRolePermissionApi(1)).json();
            if (res.code === 200) {
                if (!res.data) {
                    setShowCandidate(false);
                    return;
                }
                setShowCandidate(true);
            }
        };
        checkCandidatePermission();
    }, []);

    const CollapsedBtn = () => {
        // 获取当前路径，判断是否在聊天页面
        const isChatPage = pathname.startsWith(Path.Chat);

        // 在组件挂载时将按钮添加到标题区域（仅在聊天页面）
        useEffect(() => {
            // 只在聊天页面通过标题组件显示按钮
            if (isChatPage) {
                const titleComponent = (
                    <Tooltip title="展开侧栏" placement="bottom" styles={{ root: { fontSize: "12px" } }}>
                        <Button
                            icon={<ResizeIcon />}
                            type="text"
                            onClick={toggleCollapse}
                            style={{ marginRight: "8px" }}
                        />
                    </Tooltip>
                );

                // 添加到标题组件
                addTitleComponent("collapsedBtn", titleComponent);

                // 在组件卸载时移除
                return () => {
                    removeTitleComponent("collapsedBtn");
                };
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [isChatPage]); // 当页面路径变化时重新执行

        // 在聊天页面，不显示fixed定位的按钮，在其他页面显示
        if (isChatPage) {
            return null;
        }

        // 在非聊天页面，返回fixed定位的按钮
        return (
            <div className={styles["collapsed-button-container"]}>
                <Tooltip title="展开侧栏" placement="right" styles={{ root: { fontSize: "12px" } }}>
                    <Button
                        icon={<ResizeIcon />}
                        type="text"
                        onClick={toggleCollapse}
                        className={styles["collapse-button-standalone"]}
                    ></Button>
                </Tooltip>
            </div>
        );
    };
    // SideBarContainer 始终渲染以支持过渡动画
    // 独立的展开按钮根据 isCollapsed 状态渲染

    const showExpandButtonInBody = (shouldNarrow || (shouldSuperNarrow && !isHovered)) && !isCollapsed; // 计算是否显示按钮的条件，悬停时不显示

    return (
        <>
            {isCollapsed && <CollapsedBtn />}
            <SideBarContainer
                onDragStart={onDragStart}
                shouldNarrow={shouldNarrow}
                shouldSuperNarrow={shouldSuperNarrow}
                isCollapsed={isCollapsed} // 将 isCollapsed 传递给 SideBarContainer
                className={props.className} // 传递 className
                onHoverChange={setIsHovered} // 传递悬停状态变化回调
            >
                <SideBarHeader
                    shouldNarrow={shouldNarrow}
                    shouldSuperNarrow={shouldSuperNarrow}
                    isCollapsed={isCollapsed} // SideBarHeader 也需要 isCollapsed 来控制其内部元素
                    toggleCollapse={toggleCollapse}
                    isHovered={isHovered} // 传递悬停状态
                    pinSideBarExpanded={pinSideBarExpanded} // 传递固定展开函数
                >
                    <div className={styles["sidebar-header-bar"]}>
                        {/* 新建聊天按钮 */}
                        <IconButton
                            icon={<NewChatIcon />}
                            text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : Locale.Home.NewChat}
                            className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                pathname === Path.NewChat ? styles["new-chat-button-active"] : ""
                            }`}
                            textStyle={{ fontSize: "14px" }}
                            iconStyle={{ width: "20px", height: "20px" }}
                            onClick={() => {
                                chatStore.newSession();
                                router.push(Path.NewChat);
                            }}
                        />
                        <IconButton
                            icon={<WorkSpaceIcon />}
                            text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : Locale.Home.WorkSpace}
                            className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                pathname === Path.Dashboard ? styles["new-chat-button-active"] : ""
                            }`}
                            textStyle={{ fontSize: "14px" }}
                            iconStyle={{ width: "20px", height: "20px" }}
                            onClick={() => {
                                update((config) => {
                                    config.sidebarWidth = MIN_SIDEBAR_WIDTH;
                                    config.prevSidebarWidth = MIN_SIDEBAR_WIDTH;
                                });

                                router.push(Path.Dashboard);
                            }}
                        />
                        {showCandidate && (
                            <IconButton
                                icon={<CandidateIcon />}
                                text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : "候选人"}
                                className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                    pathname === Path.Candidate ? styles["new-chat-button-active"] : ""
                                }`}
                                textStyle={{ fontSize: "14px" }}
                                iconStyle={{ width: "20px", height: "20px" }}
                                onClick={() => {
                                    update((config) => {
                                        config.sidebarWidth = MIN_SIDEBAR_WIDTH;
                                        config.prevSidebarWidth = MIN_SIDEBAR_WIDTH;
                                    });

                                    router.push(Path.Candidate);
                                }}
                            />
                        )}
                        <IconButton
                            icon={<TalentIcon />}
                            text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : Locale.Home.Talent}
                            className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                pathname === Path.TalentPool ? styles["new-chat-button-active"] : ""
                            }`}
                            textStyle={{ fontSize: "14px" }}
                            iconStyle={{ width: "20px", height: "20px" }}
                            onClick={() => {
                                update((config) => {
                                    config.sidebarWidth = MIN_SIDEBAR_WIDTH;
                                    config.prevSidebarWidth = MIN_SIDEBAR_WIDTH;
                                });

                                router.push(Path.TalentPool);
                            }}
                        />

                        <IconButton
                            icon={<EliminationPoolIcon />}
                            text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : "淘汰池"}
                            className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                pathname === Path.EliminationPool ? styles["new-chat-button-active"] : ""
                            }`}
                            textStyle={{ fontSize: "14px" }}
                            iconStyle={{ width: "20px", height: "20px" }}
                            onClick={() => {
                                update((config) => {
                                    config.sidebarWidth = MIN_SIDEBAR_WIDTH;
                                    config.prevSidebarWidth = MIN_SIDEBAR_WIDTH;
                                });

                                router.push(Path.EliminationPool);
                            }}
                        />

                        <IconButton
                            icon={<InterviewDashboardIcon />}
                            text={shouldNarrow || (shouldSuperNarrow && !isHovered) ? undefined : "面试官"}
                            className={`${styles["sidebar-bar-button"]} ${styles["new-chat-button"]} ${
                                pathname === Path.Interviewer ? styles["new-chat-button-active"] : ""
                            }`}
                            textStyle={{ fontSize: "14px" }}
                            iconStyle={{ width: "20px", height: "20px" }}
                            onClick={() => {
                                update((config) => {
                                    config.sidebarWidth = MIN_SIDEBAR_WIDTH;
                                    config.prevSidebarWidth = MIN_SIDEBAR_WIDTH;
                                });

                                router.push(Path.Interviewer);
                            }}
                        />
                    </div>
                </SideBarHeader>
                {/* 侧边栏主体 */}
                <SideBarBody
                    onClick={(e) => {
                        if (e.target === e.currentTarget) {
                            // if (isCollapsed) toggleCollapse(); // 如果是折叠状态，先展开 -> 这个逻辑可能需要调整或移除，因为现在有了专门的展开按钮
                            router.push(Path.Home);
                        }
                    }}
                    shouldShowExpandButton={showExpandButtonInBody}
                    onExpand={expandSideBarToDefault}
                >
                    <div
                        className={clsx(styles["history-title"], {
                            [styles["history-title-super-narrow"]]: shouldSuperNarrow && !isHovered,
                        })}
                    >
                        {!isCollapsed && <Icon component={() => <RecordIcon />} />}
                        {!isCollapsed && !(shouldSuperNarrow && !isHovered) && "历史对话"}
                    </div>
                    {!isCollapsed && !(shouldSuperNarrow && !isHovered) && <ChatList narrow={shouldNarrow} />}
                </SideBarBody>
                {/* 侧边栏底部 */}
                <SideBarTail
                    name="username"
                    avatar="qqq"
                    shouldSuperNarrow={shouldSuperNarrow}
                    isCollapsed={isCollapsed}
                    isHovered={isHovered}
                />
            </SideBarContainer>
        </>
    );
}
