import { forwardRef, useEffect, useImperativeHandle, useLayoutEffect, useRef, useState } from "react";
import { RespUser, UserSelectExpose } from "@/typing";
import { Flex, Layout, Tag, Tree } from "antd";
import { RespPaginationParams, RespParams } from "@/types/typing";
import { getAllUserListApi, getDepartmentListApi } from "@/request/modules/common";
import ProTable, { ActionType, ProColumnType } from "@ant-design/pro-table";
import { getTableScroll } from "@/utils";
import { Department } from "@/store/modules/dashboard/jobFlow";
import ResizableContainer from "@/ResizableContainer";
import { TextView } from "@/TextView";
import styles from "./index.module.scss";
import { nanoid } from "nanoid";

const UserSelect = forwardRef<UserSelectExpose, { mode?: string }>((props, ref) => {
    let { mode } = props;
    if (!mode) {
        mode = "radio";
    }

    useImperativeHandle(ref, () => ({
        tableRef: tableRef.current,
        getSelectedUser: currentUser,
        setSelectedUser: (user: any[]) => {
            setCurrentUser(user);
        },
    }));

    const tableRef = useRef<ActionType>(null);
    const [scrollY, setScrollY] = useState<number>();
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [jobList, setJobList] = useState<Department[]>([]);
    const [currentUser, setCurrentUser] = useState<RespUser[]>([]);
    const [id, setId] = useState<string>("");

    const columns: ProColumnType<RespUser>[] = [
        {
            title: "工号",
            dataIndex: "empId",
            search: false,
        },
        {
            title: "姓名",
            dataIndex: "empName",
            search: false,
        },
        {
            title: "部门",
            dataIndex: "deptName",
            search: false,
        },
        {
            title: "",
            dataIndex: "empName",
            fieldProps: {
                placeholder: "请输入工号/姓名",
            },
            hideInTable: true,
        },
    ];

    useEffect(() => {
        getAllJobList();
        setId(nanoid());
    }, []);

    useLayoutEffect(() => {
        setScrollY(getTableScroll({ id: id }));
    }, [id, currentUser]);

    useEffect(() => {
        tableRef.current?.reload();
    }, [selectedKeys]);

    const getAllJobList = async () => {
        const res: RespParams<Department> = await (await getDepartmentListApi()).json();

        if (res.code === 200) {
            setSelectedKeys([res.data.dept_code_path]);
            setExpandedKeys([res.data.dept_code_path]);
            setJobList([res.data]);
            tableRef.current?.reload();
        }
    };

    return (
        <div className={styles["user-select-wrapper"]}>
            <Layout>
                <ResizableContainer
                    resizable={{ right: true }}
                    minWidth="20%"
                    maxWidth="26%"
                    initialWidth="20%"
                    initialHeight="600"
                >
                    <Layout.Sider width="100%">
                        <Tree
                            blockNode
                            selectedKeys={selectedKeys}
                            expandedKeys={expandedKeys}
                            treeData={jobList as any[]}
                            fieldNames={{ key: "dept_code_path", title: "dept_name" }}
                            onExpand={(expandKeys) => {
                                setExpandedKeys(expandKeys as string[]);
                            }}
                            onSelect={(selectedKeys) => {
                                if (selectedKeys.length > 0) {
                                    setSelectedKeys(selectedKeys as string[]);
                                }
                            }}
                            titleRender={(node) => {
                                return <TextView text={node.dept_name} />;
                            }}
                        />
                    </Layout.Sider>
                </ResizableContainer>
                <Layout.Content style={{ padding: 0 }}>
                    <ProTable
                        id={id}
                        actionRef={tableRef}
                        rowKey="empId"
                        columns={columns}
                        scroll={{ x: "max-content", y: scrollY }}
                        options={{ reload: false }}
                        manualRequest
                        onLoad={() => setScrollY(getTableScroll({ id: id }))}
                        pagination={{
                            defaultCurrent: 1,
                            defaultPageSize: 10,
                            hideOnSinglePage: false,
                            showSizeChanger: true,
                            pageSizeOptions: [10, 20, 50, 100],
                            showTotal: (total) => `共 ${total} 条`,
                        }}
                        rowSelection={{
                            type: mode as "radio" | "checkbox",
                            preserveSelectedRowKeys: true,
                            selectedRowKeys: currentUser ? currentUser.map((item) => item.empId) : [],
                            onChange: (selectedRowKeys, selectedRows) => {
                                if (mode === "radio") {
                                    // 单选直接覆盖
                                    setCurrentUser(selectedRows);
                                }
                                if (mode === "checkbox") {
                                    // 多选，取消选择后，清空数据
                                    if (selectedRows.length === 0) {
                                        setCurrentUser([]);
                                        return;
                                    }

                                    const userList = [...currentUser];
                                    // 解决表格组件存在的问题，当第一次携带选中数据后，selectedRows为undefined
                                    for (const item of selectedRowKeys) {
                                        // 判断选中数据是否已存在，不存在则添加数据，存在不做处理
                                        const current = userList.find((item2) => item2?.empId === item);
                                        if (!current) {
                                            const selected = selectedRows.find((item2) => item2?.empId === item);
                                            if (selected) {
                                                userList.push(selected);
                                            }
                                        }
                                    }

                                    // 取消选择数据后，过滤掉selectedRowKeys中不存在的数据
                                    setCurrentUser(userList.filter((item) => selectedRowKeys.includes(item.empId)));
                                }
                            },
                        }}
                        onRow={(record) => {
                            return {
                                onClick: () => {
                                    if (mode === "radio") {
                                        setCurrentUser([record]);
                                        return;
                                    }
                                    if (mode === "checkbox") {
                                        const current = currentUser.find((item) => item.empId === record.empId);
                                        if (current) {
                                            setCurrentUser((prev) =>
                                                prev.filter((item) => item.empId !== record.empId)
                                            );
                                        } else {
                                            setCurrentUser([...currentUser, record]);
                                        }
                                        return;
                                    }
                                },
                            };
                        }}
                        request={async (params) => {
                            const deptCodePath = selectedKeys?.[0] ?? "";

                            const res: RespPaginationParams<RespUser> = await (
                                await getAllUserListApi({
                                    pageNum: (params?.current ?? 1) - 1,
                                    pageSize: params?.pageSize ?? 10,
                                    empName: params?.empName ?? "",
                                    deptCodePath,
                                })
                            ).json();

                            if (res.code === 200) {
                                return {
                                    data: res.data.records,
                                    success: true,
                                    total: res.data.total,
                                };
                            }

                            return {
                                data: [],
                                success: false,
                                total: 0,
                            };
                        }}
                        tableAlertRender={(alerts) => {
                            const userList: RespUser[] = [];
                            const allUserList = [...currentUser, ...alerts.selectedRows.filter((item) => item)];
                            allUserList.forEach((item) => {
                                if (!userList.find((item2) => item2.empId === item.empId)) {
                                    userList.push(item);
                                }
                            });

                            return (
                                <div>
                                    <div style={{ fontWeight: 600, marginBottom: 6 }}>
                                        已选择{userList.length}条数据
                                    </div>
                                    <Flex gap={12} wrap>
                                        {userList.map((item) => {
                                            return (
                                                <Tag
                                                    key={item.empId}
                                                    color="processing"
                                                    closable
                                                    onClose={() => {
                                                        setCurrentUser((prev) =>
                                                            prev.filter((item2) => item2.empId !== item.empId)
                                                        );
                                                    }}
                                                >
                                                    {item.empId}-{item.empName}
                                                </Tag>
                                            );
                                        })}
                                    </Flex>
                                </div>
                            );
                        }}
                    />
                </Layout.Content>
            </Layout>
        </div>
    );
});
UserSelect.displayName = "UserSelect";

export default UserSelect;
