{"name": "nextchat", "private": false, "license": "mit", "scripts": {"dev": "next dev -H 0.0.0.0", "dev:ip": "node -e \"const os=require('os');const nets=os.networkInterfaces();const ip = Object.values(nets).flat().filter(i=>i.family==='IPv4'&&!i.internal)[0]?.address||'未找到IP';console.log('\\n局域网 IP 地址：http://'+ip+':3000\\n');\" && yarn dev", "build:test": "cross-env NODE_ENV=test BUILD_MODE=standalone next build", "build": "cross-env BUILD_MODE=standalone next build", "build:analysis": "cross-env ANALYZE=true next build", "start": "next start", "lint": "next lint", "export": "cross-env BUILD_MODE=export BUILD_APP=1 next build", "export:dev": "cross-env BUILD_MODE=export BUILD_APP=1 next dev", "app:dev": "yarn tauri dev", "app:build": "yarn tauri build", "app:clear": "yarn tauri dev", "prompts": "node ./scripts/fetch-prompts.mjs", "generate:pages": "node ./scripts/generate-pages.mjs", "create:app-router": "node ./scripts/create-app-router-structure.mjs", "fix:imports": "node ./scripts/fix-imports.mjs", "prepare": "husky install", "proxy-dev": "sh ./scripts/init-proxy.sh && proxychains -f ./scripts/proxychains.conf yarn dev", "test": "node --no-warnings --experimental-vm-modules $(yarn bin jest) --watch", "test:ci": "node --no-warnings --experimental-vm-modules $(yarn bin jest) --ci"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/pro-table": "^3.21.0", "@atlaskit/pragmatic-drag-and-drop": "^1.7.4", "@atlaskit/pragmatic-drag-and-drop-react-drop-indicator": "^3.2.1", "@douyinfe/semi-next": "^2.78.0", "@douyinfe/semi-ui": "^2.78.0", "@fortaine/fetch-event-source": "^3.0.6", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hello-pangea/dnd": "^16.5.0", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@modelcontextprotocol/sdk": "^1.0.4", "@next/bundle-analyzer": "^15.3.5", "@next/mdx": "^15.3.1", "@next/third-parties": "^14.1.0", "@svgr/webpack": "^6.5.1", "@typescript-eslint/eslint-plugin": "6.4.0", "@vercel/analytics": "^0.1.11", "@vercel/speed-insights": "^1.0.2", "antd": "^5.24.8", "axios": "^1.7.5", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "docx-preview": "^0.3.5", "echarts": "^5.6.0", "emoji-picker-react": "^4.9.2", "file-loader": "^6.2.0", "fuse.js": "^7.0.0", "heic2any": "^0.0.4", "html-to-image": "^1.11.11", "idb-keyval": "^6.2.1", "ky": "^1.8.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-to-txt": "^2.0.1", "mermaid": "^10.6.1", "nanoid": "^5.0.3", "next": "^14.1.1", "next-mdx-remote": "^5.0.0", "node-fetch": "^3.3.1", "openapi-client-axios": "^7.5.5", "pdfjs-dist": "3.4.120", "pptx-preview": "^1.0.5", "quill": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.15.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sass": "^1.59.2", "spark-md5": "^3.0.2", "use-debounce": "^9.0.4", "zod": "^3.24.1", "zustand": "^4.3.8"}, "devDependencies": {"@tauri-apps/api": "^2.1.1", "@tauri-apps/cli": "1.5.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/js-yaml": "4.0.9", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/mdx": "^2.0.13", "@types/node": "^20.11.30", "@types/react": "^18.2.70", "@types/react-dom": "^18.2.7", "@types/react-katex": "^3.0.0", "@types/spark-md5": "^3.0.4", "@typescript-eslint/parser": "6.4.0", "babel-plugin-import": "^1.13.8", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "8.49.0", "eslint-config-next": "13.4.19", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^13.2.2", "prettier": "^3.0.2", "ts-node": "^10.9.2", "tsx": "^4.16.0", "typescript": "5.2.2", "watch": "^1.0.2", "webpack": "^5.88.1"}, "resolutions": {"lint-staged/yaml": "^2.2.2"}, "packageManager": "yarn@1.22.19"}