#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, '../src');

// Import path mappings to fix
const IMPORT_MAPPINGS = {
    '@/app/constant': '@/constant',
    '@/app/store': '@/store',
    '@/app/utils': '@/utils',
    '@/app/config': '@/config',
    '@/app/typing': '@/types/typing',
    '@/app/lib': '@/lib',
    '@/app/hooks': '@/hooks',
    '@/app/request': '@/request',
    '@/app/components': '@/components',
    '@/app/icons': '@/icons',
    '@/app/locales': '@/locales',
    '@/app/styles': '@/styles',
    '@/app/polyfill': '@/polyfill',
    '@/app/polyfills': '@/polyfills'
};

function fixImportsInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;
        
        // Fix each import mapping
        for (const [oldPath, newPath] of Object.entries(IMPORT_MAPPINGS)) {
            const regex = new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
            if (content.includes(oldPath)) {
                content = content.replace(regex, newPath);
                modified = true;
            }
        }
        
        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ Fixed imports in: ${path.relative(SRC_DIR, filePath)}`);
            return true;
        }
        
        return false;
    } catch (error) {
        console.error(`❌ Error fixing imports in ${filePath}:`, error.message);
        return false;
    }
}

function findAndFixFiles(dir) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    let fixedCount = 0;
    
    for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
            // Skip the app directory since those are the new App Router files
            if (entry.name === 'app') {
                continue;
            }
            fixedCount += findAndFixFiles(fullPath);
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
            if (fixImportsInFile(fullPath)) {
                fixedCount++;
            }
        }
    }
    
    return fixedCount;
}

function fixAllImports() {
    console.log('🚀 Fixing import paths...\n');
    
    const fixedCount = findAndFixFiles(SRC_DIR);
    
    console.log(`\n✨ Import fixing complete!`);
    console.log(`📊 Fixed imports in ${fixedCount} files`);
    
    if (fixedCount > 0) {
        console.log('\n📋 Summary of changes:');
        for (const [oldPath, newPath] of Object.entries(IMPORT_MAPPINGS)) {
            console.log(`  ${oldPath} → ${newPath}`);
        }
    }
}

// Run the script
fixAllImports();
