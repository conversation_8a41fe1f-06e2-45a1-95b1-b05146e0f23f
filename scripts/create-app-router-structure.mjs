#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const APP_DIR = path.join(__dirname, '../src/app');

// Mapping from URL paths to existing component directories
const ROUTE_MAPPINGS = [
    { urlPath: 'auth', componentDir: 'AuthClient' },
    { urlPath: 'dashboard', componentDir: 'Dashboard' },
    { urlPath: 'talent-pool', componentDir: 'TalentPoolNew' },
    { urlPath: 'resume-detail', componentDir: 'ResumeDetail' },
    { urlPath: 'interviewer', componentDir: 'Interviewer' },
    { urlPath: 'interview-time', componentDir: 'InterviewTime' },
    { urlPath: 'candidate', componentDir: 'Candidate' },
    { urlPath: 'my-interview', componentDir: 'MyInterview' },
    { urlPath: 'job-detail', componentDir: 'JobDetail' },
    { urlPath: 'elimination-pool', componentDir: 'EliminationPool' },
    { urlPath: 'chat', componentDir: 'Chat' },
    { urlPath: 'new-chat', componentDir: 'NewChat' },
    { urlPath: 'login', componentDir: 'Login' },
    { urlPath: 'settings', componentDir: null }, // settings is a standalone file
];

function createRouteDirectory(urlPath, componentDir) {
    const routeDir = path.join(APP_DIR, urlPath);
    
    // Create the route directory if it doesn't exist
    if (!fs.existsSync(routeDir)) {
        fs.mkdirSync(routeDir, { recursive: true });
        console.log(`📁 Created directory: ${urlPath}`);
    }
    
    const pageFile = path.join(routeDir, 'page.tsx');
    
    // Check if page.tsx already exists
    if (fs.existsSync(pageFile)) {
        console.log(`ℹ️  page.tsx already exists for route: ${urlPath}`);
        return;
    }
    
    let pageContent;
    
    if (componentDir) {
        // Import from the component directory
        pageContent = `// App Router page for /${urlPath}
// Imports from the existing component in ${componentDir}

export { default } from '../${componentDir}';

// Re-export any named exports
export * from '../${componentDir}';
`;
    } else {
        // For standalone files like settings
        pageContent = `// App Router page for /${urlPath}
// Imports from the existing component file

export { default } from '../${urlPath}';

// Re-export any named exports  
export * from '../${urlPath}';
`;
    }
    
    try {
        fs.writeFileSync(pageFile, pageContent);
        console.log(`✅ Generated page.tsx for route: /${urlPath}`);
    } catch (error) {
        console.error(`❌ Failed to generate page.tsx for route: /${urlPath}`, error);
    }
}

function createAppRouterStructure() {
    console.log('🚀 Creating Next.js App Router structure...\n');
    
    ROUTE_MAPPINGS.forEach(({ urlPath, componentDir }) => {
        createRouteDirectory(urlPath, componentDir);
    });
    
    console.log('\n✨ App Router structure creation complete!');
    console.log('\n📋 Summary:');
    console.log('- Created route directories with kebab-case names for URLs');
    console.log('- Generated page.tsx files that import from existing PascalCase components');
    console.log('- Maintained your existing index.tsx naming convention');
    console.log('- Ready for Next.js App Router with custom naming!');
}

// Run the script
createAppRouterStructure();
