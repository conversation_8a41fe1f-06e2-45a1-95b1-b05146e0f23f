#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const APP_DIR = path.join(__dirname, '../src/app');

// Routes that should have page.tsx generated from index.tsx
// Using actual directory names from the codebase
const ROUTES_TO_GENERATE = [
    'AuthClient',
    'Dashboard',
    'TalentPoolNew',
    'ResumeDetail',
    'Interviewer',
    'InterviewTime',
    'Candidate',
    'MyInterview',
    'JobDetail',
    'EliminationPool',
    'Chat',
    'NewChat',
    'Login'
];

function generatePageFile(routePath) {
    const routeDir = path.join(APP_DIR, routePath);
    const indexFile = path.join(routeDir, 'index.tsx');
    const pageFile = path.join(routeDir, 'page.tsx');

    // Check if index.tsx exists
    if (!fs.existsSync(indexFile)) {
        console.log(`⚠️  No index.tsx found for route: ${routePath}`);
        return;
    }

    // Check if page.tsx already exists
    if (fs.existsSync(pageFile)) {
        console.log(`ℹ️  page.tsx already exists for route: ${routePath}`);
        return;
    }

    // Create the route directory if it doesn't exist
    if (!fs.existsSync(routeDir)) {
        fs.mkdirSync(routeDir, { recursive: true });
    }

    // Generate page.tsx content that imports from index.tsx
    const pageContent = `// Auto-generated page.tsx that imports from index.tsx
// This file enables Next.js App Router to work with index.tsx naming convention

export { default } from './index';

// Re-export any named exports from index.tsx
export * from './index';
`;

    try {
        fs.writeFileSync(pageFile, pageContent);
        console.log(`✅ Generated page.tsx for route: ${routePath}`);
    } catch (error) {
        console.error(`❌ Failed to generate page.tsx for route: ${routePath}`, error);
    }
}

function generateAllPages() {
    console.log('🚀 Generating page.tsx files from index.tsx files...\n');

    ROUTES_TO_GENERATE.forEach(route => {
        generatePageFile(route);
    });

    console.log('\n✨ Page generation complete!');
}

// Run the script
generateAllPages();
