import webpack from "webpack";
import withBundleAnalyzer from '@next/bundle-analyzer';
import { nanoid } from "nanoid";
import path from "path";
import fs from "fs";

const isProduction = process.env.NEXT_PUBLIC_NODE_ENV === "production";
const mode = process.env.BUILD_MODE ?? "standalone";
console.log("[Next] build mode", mode);

// const disableChunk = !!process.env.DISABLE_CHUNK || mode === "export";
// console.log("[Next] build with chunk: ", !disableChunk);

// Generate timestamp for cache busting
const buildTimestamp = new Date().getTime();
console.log("[Next] build timestamp:", buildTimestamp);

// Custom route resolution for index.tsx files
function createIndexToPagePlugin() {
    return new webpack.NormalModuleReplacementPlugin(
        /^\.\/page$/,
        (resource) => {
            const context = resource.context;
            const indexPath = path.join(context, 'index.tsx');
            const pagePath = path.join(context, 'page.tsx');

            // If index.tsx exists but page.tsx doesn't, use index.tsx
            if (fs.existsSync(indexPath) && !fs.existsSync(pagePath)) {
                resource.request = './index';
            }
        }
    );
}

const bundleAnalyzer = withBundleAnalyzer({
    enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
    webpack(config, { dev, isServer }) {
        // 确保 PDF.js worker 文件被正确处理
        config.module.rules.push({
            test: /pdf\.worker\.(min\.)?js/,
            type: 'asset/resource',
            generator: {
                filename: 'static/worker/[hash][ext][query]'
            }
        });
        config.module.rules.push(
            {
                test: /\.svg$/,
                use: ["@svgr/webpack"],
            },
            { test: /\.node$/, use: "file-loader" }
        );

        // 禁用的原因是因为会导致不必要的计算和处理，导致加载或打包变慢
        // if (disableChunk) {
        //     config.plugins.push(new webpack.optimize.LimitChunkCountPlugin({ maxChunks: 1 }));
        // } else {
        //     // Optimize chunk splitting for better loading performance
        //     config.optimization = {
        //         ...config.optimization,
        //         splitChunks: {
        //             ...config.optimization.splitChunks,
        //             chunks: 'all',
        //             cacheGroups: {
        //                 ...config.optimization.splitChunks?.cacheGroups,
        //                 // Vendor chunk for stable third-party libraries
        //                 vendor: {
        //                     test: /[\\/]node_modules[\\/]/,
        //                     name: 'vendors',
        //                     chunks: 'all',
        //                     priority: 10,
        //                     reuseExistingChunk: true,
        //                 },
        //                 // Common chunk for shared code
        //                 common: {
        //                     name: 'common',
        //                     minChunks: 2,
        //                     chunks: 'all',
        //                     priority: 5,
        //                     reuseExistingChunk: true,
        //                 },
        //                 // Large libraries get their own chunks
        //                 antd: {
        //                     test: /[\\/]node_modules[\\/](antd|@ant-design)[\\/]/,
        //                     name: 'antd',
        //                     chunks: 'all',
        //                     priority: 15,
        //                 },
        //                 react: {
        //                     test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
        //                     name: 'react',
        //                     chunks: 'all',
        //                     priority: 20,
        //                 },
        //             },
        //         },
        //     };
        // }

        // Cache busting and filename optimization
        if (!dev && !isServer) {
            // Add timestamp-based cache busting for production builds
            config.output = {
                ...config.output,
                filename: `static/chunks/[name].[contenthash].js`,
                chunkFilename: `static/chunks/[name].[contenthash].js`,
            };
        }

        // Improve chunk loading reliability
        config.plugins.push(
            new webpack.DefinePlugin({
                __BUILD_TIMESTAMP__: JSON.stringify(buildTimestamp),
                __CHUNK_RETRY_CONFIG__: JSON.stringify({
                    maxRetries: 3,
                    retryDelay: 1000,
                }),
            }),
            // Add custom index.tsx to page.tsx resolution
            createIndexToPagePlugin()
        );

        config.resolve.fallback = {
            child_process: false,
            canvas: false, // pdf.js需要canvas，在浏览器环境中将其设为false
            fs: false,
            http: false,
            https: false,
            url: false,
            zlib: false,
            stream: false,
            crypto: false,
        };

        // 解决PDF.js的模块问题 - 不使用 require.resolve
        config.resolve.alias = {
            ...config.resolve.alias,
            // 直接使用模块名称而不是 require.resolve
            "pdfjs-dist": "pdfjs-dist",
        };

        return config;
    },
    output: mode,
    images: {
        unoptimized: mode === "export",
    },
    experimental: {
        forceSwcTransforms: true,
        proxyTimeout: 120_000, // 设置nextjs项目请求代理超时时间为120s
    },
    compiler: {
        // 生产环境去除console.log打印
        removeConsole: isProduction,
    },
    generateEtags: false, // 禁用etag
    generateBuildId: async () => {
        return nanoid() + '_' + buildTimestamp;
    }
};

const CorsHeaders = [
    { key: "Access-Control-Allow-Credentials", value: "true" },
    { key: "Access-Control-Allow-Origin", value: "*" },
    {
        key: "Access-Control-Allow-Methods",
        value: "*",
    },
    {
        key: "Access-Control-Allow-Headers",
        value: "*",
    },
    {
        key: "Access-Control-Max-Age",
        value: "86400",
    },
];

if (mode !== "export") {
    nextConfig.headers = async () => {
        return [
            {
                source: "/api/:path*",
                headers: CorsHeaders,
            },
        ];
    };

    nextConfig.rewrites = async () => {
        const ret = [
            // adjust for previous version directly using "/api/proxy/" as proxy base route
            // {
            //   source: "/api/proxy/v1/:path*",
            //   destination: "https://api.openai.com/v1/:path*",
            // },
            {
                // https://{resource_name}.openai.azure.com/openai/deployments/{deploy_name}/chat/completions
                source: "/api/proxy/azure/:resource_name/deployments/:deploy_name/:path*",
                destination: "https://:resource_name.openai.azure.com/openai/deployments/:deploy_name/:path*",
            },
            {
                source: "/api/proxy/google/:path*",
                destination: "https://generativelanguage.googleapis.com/:path*",
            },
            {
                source: "/api/proxy/openai/:path*",
                destination: "https://api.openai.com/:path*",
            },
            {
                source: "/api/proxy/anthropic/:path*",
                destination: "https://api.anthropic.com/:path*",
            },
            {
                source: "/google-fonts/:path*",
                destination: "https://fonts.googleapis.com/:path*",
            },
            {
                source: "/sharegpt",
                destination: "https://sharegpt.com/api/conversations",
            },
            {
                source: "/api/proxy/alibaba/:path*",
                destination: "https://dashscope.aliyuncs.com/api/:path*",
            },
            {
                source: "/api/proxy/:path*",
                destination: "https://api.openai.com/:path*",
            },
            {
                source: "/dev-api/:path*",
                destination: "http://10.32.228.50:8888/api/v1alpha1/:path*",    // 测试
                // destination: "http://10.30.38.113:8888/api/v1alpha1/:path*", // luxingyu
                // destination: "http://10.30.32.227:8888/api/v1alpha1/:path*", // liuwenpeng
                // destination: "http://10.30.38.198:8888/api/v1alpha1/:path*", // xielongjie
                // destination: "http://10.30.34.114:8888/api/v1alpha1/:path*", // chenhao
                // destination: "https://hrgenius.inovance.com/prod-api/:path*", // 生产
            },
            {
                source: "/dev-api-chat/:path*",
                destination: "http://10.30.36.12:7777/api/v1alpha1/:path*",
            },
        ];

        return {
            beforeFiles: ret,
        };
    };

    nextConfig.reactStrictMode = process.env.NEXT_PUBLIC_NODE_ENV === "production";
    nextConfig.compress = process.env.NEXT_PUBLIC_NODE_ENV === "production";

    nextConfig.env = {
        NEXT_PUBLIC_FETCH_BASE_API: process.env.NEXT_PUBLIC_FETCH_BASE_API,
        NEXT_PUBLIC_FETCH_CHAT_API: process.env.NEXT_PUBLIC_FETCH_CHAT_API,
        NEXT_PUBLIC_AES_SECRET_KEY: process.env.NEXT_PUBLIC_AES_SECRET_KEY,
        NEXT_PUBLIC_CAS_URL: process.env.NEXT_PUBLIC_CAS_URL,
        NEXT_PUBLIC_CAS_CLIENT_ID: process.env.NEXT_PUBLIC_CAS_CLIENT_ID,
        NEXT_PUBLIC_CAS_SERVICE_URL: process.env.NEXT_PUBLIC_CAS_SERVICE_URL,
        NEXT_PUBLIC_AES_IV: process.env.NEXT_PUBLIC_AES_IV,
        NEXT_PUBLIC_ADMIN_URL: process.env.NEXT_PUBLIC_ADMIN_URL,
        NEXT_PUBLIC_NODE_ENV: process.env.NEXT_PUBLIC_NODE_ENV,
    };
}
export default bundleAnalyzer(nextConfig);